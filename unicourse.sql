-- =============================================
-- FUNDAMENTAL UNIVERSITY COURSE MANAGEMENT SYSTEM
-- =============================================
-- This is a basic/fundamental level database for learning SQL
-- Focus: Basic tables, simple relationships, core SQL operations

CREATE DATABASE UNIVERSITY;
USE UNIVERSITY;

-- =============================================
-- CORE TABLES (Fundamental Level)
-- =============================================

-- DEPARTMENTS
CREATE TABLE Departments (
    DepartmentID INT PRIMARY KEY IDENTITY,
    Name NVARCHAR(100) NOT NULL,
    Building NVARCHAR(50)
);

-- STUDENTS
CREATE TABLE Students (
    StudentID INT PRIMARY KEY IDENTITY,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100),
    EnrollmentYear INT,
    DepartmentID INT FOREIGN KEY REFERENCES Departments(DepartmentID)
);

-- LECTURERS
CREATE TABLE Lecturers (
    LecturerID INT PRIMARY KEY IDENTITY,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100),
    DepartmentID INT FOREIGN KEY REFERENCES Departments(DepartmentID)
);

-- COURSES
CREATE TABLE Courses (
    CourseID INT PRIMARY KEY IDENTITY,
    Code NVARCHAR(10) UNIQUE NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    Credits INT CHECK (Credits BETWEEN 1 AND 6),
    DepartmentID INT FOREIGN KEY REFERENCES Departments(DepartmentID),
    LecturerID INT FOREIGN KEY REFERENCES Lecturers(LecturerID)
);

-- ENROLLMENTS (Many-to-many relationship)
CREATE TABLE Enrollments (
    StudentID INT,
    CourseID INT,
    EnrollmentDate DATE DEFAULT GETDATE(),
    PRIMARY KEY(StudentID, CourseID),
    FOREIGN KEY(StudentID) REFERENCES Students(StudentID),
    FOREIGN KEY(CourseID) REFERENCES Courses(CourseID)
);

-- GRADES
CREATE TABLE Grades (
    StudentID INT,
    CourseID INT,
    Grade DECIMAL(3,1) CHECK (Grade BETWEEN 0.0 AND 4.0), -- Standard 0-4 GPA scale
    PRIMARY KEY(StudentID, CourseID),
    FOREIGN KEY(StudentID) REFERENCES Students(StudentID),
    FOREIGN KEY(CourseID) REFERENCES Courses(CourseID)
);
