-- =============================================
-- UNIVERSITY COURSE MANAGEMENT SYSTEM DATABASE
-- =============================================
-- Complete SQL Server Implementation for Fundamental Learning
-- Compatible with SQL Server Management Studio (SSMS)
-- Focus: T-SQL syntax, proper relationships, comprehensive sample data
-- Target: Beginner-friendly SQL learning with realistic business scenarios

-- =============================================
-- DATABASE CREATION AND SETUP
-- =============================================

-- Create the database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UNIVERSITY')
BEGIN
    CREATE DATABASE UNIVERSITY;
END
GO

USE UNIVERSITY;
GO

-- =============================================
-- TABLE CREATION WITH PROPER T-SQL SYNTAX
-- =============================================

-- DEPARTMENTS TABLE
-- Purpose: Central organizing entity for academic departments
-- Business Rule: Each department has a unique name and building location
CREATE TABLE Departments (
    DepartmentID INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL UNIQUE,
    Building NVARCHAR(50) NOT NULL,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT CK_Departments_Name_NotEmpty CHECK (LEN(TRIM(Name)) > 0),
    CONSTRAINT CK_Departments_Building_NotEmpty CHECK (LEN(TRIM(Building)) > 0)
);

-- STUDENTS TABLE
-- Purpose: Store student information and link to their primary department
-- Business Rule: Students must have unique email addresses and belong to a department
CREATE TABLE Students (
    StudentID INT IDENTITY(1,1) PRIMARY KEY,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100) NOT NULL UNIQUE,
    EnrollmentYear INT NOT NULL,
    DepartmentID INT NOT NULL,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Students_Department FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID),
    CONSTRAINT CK_Students_FirstName_NotEmpty CHECK (LEN(TRIM(FirstName)) > 0),
    CONSTRAINT CK_Students_LastName_NotEmpty CHECK (LEN(TRIM(LastName)) > 0),
    CONSTRAINT CK_Students_Email_Format CHECK (Email LIKE '%@%.%'),
    CONSTRAINT CK_Students_EnrollmentYear CHECK (EnrollmentYear BETWEEN 2020 AND YEAR(GETDATE()) + 1)
);

-- LECTURERS TABLE
-- Purpose: Store lecturer information and departmental affiliation
-- Business Rule: Lecturers must have unique email addresses and belong to a department
CREATE TABLE Lecturers (
    LecturerID INT IDENTITY(1,1) PRIMARY KEY,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100) NOT NULL UNIQUE,
    DepartmentID INT NOT NULL,
    HireDate DATE DEFAULT CAST(GETDATE() AS DATE),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Lecturers_Department FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID),
    CONSTRAINT CK_Lecturers_FirstName_NotEmpty CHECK (LEN(TRIM(FirstName)) > 0),
    CONSTRAINT CK_Lecturers_LastName_NotEmpty CHECK (LEN(TRIM(LastName)) > 0),
    CONSTRAINT CK_Lecturers_Email_Format CHECK (Email LIKE '%@%.%'),
    CONSTRAINT CK_Lecturers_HireDate CHECK (HireDate <= GETDATE())
);

-- COURSES TABLE
-- Purpose: Store course information with department and lecturer assignments
-- Business Rule: Course codes must be unique, credits between 1-6, must have department and lecturer
CREATE TABLE Courses (
    CourseID INT IDENTITY(1,1) PRIMARY KEY,
    Code NVARCHAR(10) NOT NULL UNIQUE,
    Name NVARCHAR(100) NOT NULL,
    Credits INT NOT NULL,
    DepartmentID INT NOT NULL,
    LecturerID INT NOT NULL,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Courses_Department FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID),
    CONSTRAINT FK_Courses_Lecturer FOREIGN KEY (LecturerID) REFERENCES Lecturers(LecturerID),
    CONSTRAINT CK_Courses_Code_NotEmpty CHECK (LEN(TRIM(Code)) > 0),
    CONSTRAINT CK_Courses_Name_NotEmpty CHECK (LEN(TRIM(Name)) > 0),
    CONSTRAINT CK_Courses_Credits CHECK (Credits BETWEEN 1 AND 6)
);

-- ENROLLMENTS TABLE
-- Purpose: Many-to-many relationship between Students and Courses
-- Business Rule: Students can enroll in multiple courses, courses can have multiple students
CREATE TABLE Enrollments (
    StudentID INT NOT NULL,
    CourseID INT NOT NULL,
    EnrollmentDate DATE DEFAULT CAST(GETDATE() AS DATE),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    PRIMARY KEY (StudentID, CourseID),
    CONSTRAINT FK_Enrollments_Student FOREIGN KEY (StudentID) REFERENCES Students(StudentID),
    CONSTRAINT FK_Enrollments_Course FOREIGN KEY (CourseID) REFERENCES Courses(CourseID),
    CONSTRAINT CK_Enrollments_Date CHECK (EnrollmentDate <= GETDATE())
);

-- GRADES TABLE
-- Purpose: Store final grades for student-course combinations
-- Business Rule: Grades use 4.0 GPA scale, students can only have one grade per course
CREATE TABLE Grades (
    StudentID INT NOT NULL,
    CourseID INT NOT NULL,
    Grade DECIMAL(3,1) NOT NULL,
    GradeDate DATE DEFAULT CAST(GETDATE() AS DATE),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    PRIMARY KEY (StudentID, CourseID),
    CONSTRAINT FK_Grades_Student FOREIGN KEY (StudentID) REFERENCES Students(StudentID),
    CONSTRAINT FK_Grades_Course FOREIGN KEY (CourseID) REFERENCES Courses(CourseID),
    CONSTRAINT CK_Grades_Range CHECK (Grade BETWEEN 0.0 AND 4.0),
    CONSTRAINT CK_Grades_Date CHECK (GradeDate <= GETDATE())
);

GO
