CREATE DATABASE UNIVERSITY;
USE UNIVERSITY;

-- DEPARTMENTS
CREATE TABLE Departments (
    DepartmentID INT PRIMARY KEY IDENTITY,
    Name NVARCHAR(100) NOT NULL,
    Building NVARCHAR(50)
);

-- LECTURERS
CREATE TABLE Lecturers (
    LecturerID INT PRIMARY KEY IDENTITY,
    FirstName NVARCHAR(50),
    LastName NVARCHAR(50),
    Title NVARCHAR(50),
    DepartmentID INT FOREIGN KEY REFERENCES Departments(DepartmentID)
);

-- STUDENTS
CREATE TABLE Students (
    StudentID INT PRIMARY KEY IDENTITY,
    FirstName NVARCHAR(50),
    LastName NVARCHAR(50),
    EnrollmentYear INT,
    DepartmentID INT FOREIGN KEY REFERENCES Departments(DepartmentID)
);

-- COURSES
CREATE TABLE Courses (
    CourseID INT PRIMARY KEY IDENTITY,
    Code NVARCHAR(10) UNIQUE,
    Name NVARCHAR(100),
    Credits INT CHECK (Credits BETWEEN 1 AND 10),
    DepartmentID INT FOREIGN KEY REFERENCES Departments(DepartmentID),
    LecturerID INT FOREIGN KEY REFERENCES Lecturers(LecturerID)
);

-- ENROLLMENTS (Many-to-many)
CREATE TABLE Enrollments (
    StudentID INT,
    CourseID INT,
    EnrollmentDate DATE DEFAULT GETDATE(),
    PRIMARY KEY(StudentID, CourseID),
    FOREIGN KEY(StudentID) REFERENCES Students(StudentID),
    FOREIGN KEY(CourseID) REFERENCES Courses(CourseID)
);

-- GRADES
CREATE TABLE Grades (
    StudentID INT,
    CourseID INT,
    Grade DECIMAL(3,1) CHECK (Grade BETWEEN 2.0 AND 5.0),
    GradeDate DATE DEFAULT GETDATE(),
    PRIMARY KEY(StudentID, CourseID),
    FOREIGN KEY(StudentID) REFERENCES Students(StudentID),
    FOREIGN KEY(CourseID) REFERENCES Courses(CourseID)
);

-- COURSE SCHEDULE
CREATE TABLE CourseSchedule (
    ScheduleID INT PRIMARY KEY IDENTITY,
    CourseID INT FOREIGN KEY REFERENCES Courses(CourseID),
    Room NVARCHAR(10),
    DayOfWeek NVARCHAR(10),
    StartTime TIME,
    EndTime TIME
);
