# University Course Management System Database Documentation

## 📋 **Project Overview**

This is a **fundamental-level** University Course Management System database designed specifically for SQL beginners. The project demonstrates core database concepts including table relationships, foreign keys, constraints, and realistic business scenarios while maintaining simplicity for educational purposes.

### **🎯 Learning Objectives**
- Understand basic database design principles
- Learn fundamental SQL operations (SELECT, INSERT, UPDATE, DELETE)
- Practice JOIN operations between related tables
- Explore aggregate functions and GROUP BY clauses
- Work with realistic sample data and business scenarios

---

## 🏗️ **Database Architecture**

### **Core Design Principles**
1. **Simplicity First**: Focus on fundamental concepts without overwhelming complexity
2. **Realistic Relationships**: Mirror real university business processes
3. **Data Integrity**: Proper constraints and referential integrity
4. **Educational Value**: Clear examples for learning SQL concepts

### **Technology Stack**
- **Database Engine**: Microsoft SQL Server
- **Syntax**: T-SQL (Transact-SQL)
- **Compatibility**: SQL Server Management Studio (SSMS)
- **Data Types**: NVARCHAR for Unicode support, IDENTITY for auto-increment

---

## 📊 **Entity Relationship Design**

### **🟣 DEPARTMENTS Table (Central Hub)**
**Purpose**: Organizes the university into academic departments

| Column | Data Type | Constraints | Description |
|--------|-----------|-------------|-------------|
| DepartmentID | INT | PK, IDENTITY(1,1) | Unique department identifier |
| Name | NVARCHAR(100) | NOT NULL, UNIQUE | Department name (e.g., "Computer Science") |
| Building | NVARCHAR(50) | NOT NULL | Physical location |
| CreatedDate | DATETIME2 | DEFAULT GETDATE() | Record creation timestamp |

**Business Rules**:
- Each department must have a unique name
- Departments are housed in specific buildings
- Serves as the organizational backbone for students, lecturers, and courses

### **🟠 LECTURERS Table (Faculty)**
**Purpose**: Stores faculty information and departmental affiliations

| Column | Data Type | Constraints | Description |
|--------|-----------|-------------|-------------|
| LecturerID | INT | PK, IDENTITY(1,1) | Unique lecturer identifier |
| FirstName | NVARCHAR(50) | NOT NULL | Lecturer's first name |
| LastName | NVARCHAR(50) | NOT NULL | Lecturer's last name |
| Email | NVARCHAR(100) | NOT NULL, UNIQUE | University email address |
| DepartmentID | INT | FK, NOT NULL | References Departments table |
| HireDate | DATE | DEFAULT GETDATE() | Employment start date |
| CreatedDate | DATETIME2 | DEFAULT GETDATE() | Record creation timestamp |

**Business Rules**:
- Each lecturer belongs to exactly one department
- Email addresses must be unique across all lecturers
- Lecturers can teach multiple courses within their department

### **🔵 STUDENTS Table (Learners)**
**Purpose**: Manages student information and departmental enrollment

| Column | Data Type | Constraints | Description |
|--------|-----------|-------------|-------------|
| StudentID | INT | PK, IDENTITY(1,1) | Unique student identifier |
| FirstName | NVARCHAR(50) | NOT NULL | Student's first name |
| LastName | NVARCHAR(50) | NOT NULL | Student's last name |
| Email | NVARCHAR(100) | NOT NULL, UNIQUE | University email address |
| EnrollmentYear | INT | NOT NULL, CHECK | Year student enrolled (2020-current+1) |
| DepartmentID | INT | FK, NOT NULL | Primary department affiliation |
| CreatedDate | DATETIME2 | DEFAULT GETDATE() | Record creation timestamp |

**Business Rules**:
- Students have a primary department (major)
- Can enroll in courses from other departments (electives/minors)
- Email addresses must be unique across all students
- Enrollment year must be realistic (2020 to next year)

### **🟢 COURSES Table (Academic Content)**
**Purpose**: Defines courses offered by the university

| Column | Data Type | Constraints | Description |
|--------|-----------|-------------|-------------|
| CourseID | INT | PK, IDENTITY(1,1) | Unique course identifier |
| Code | NVARCHAR(10) | NOT NULL, UNIQUE | Course code (e.g., "CS101") |
| Name | NVARCHAR(100) | NOT NULL | Full course name |
| Credits | INT | NOT NULL, CHECK(1-6) | Credit hours (1-6 range) |
| DepartmentID | INT | FK, NOT NULL | Offering department |
| LecturerID | INT | FK, NOT NULL | Assigned instructor |
| CreatedDate | DATETIME2 | DEFAULT GETDATE() | Record creation timestamp |

**Business Rules**:
- Course codes must be unique across the university
- Each course belongs to one department and has one primary lecturer
- Credit hours follow standard university ranges (1-6)
- Courses are taught by lecturers from the same department

### **🟡 ENROLLMENTS Table (Many-to-Many Bridge)**
**Purpose**: Links students to courses they're taking

| Column | Data Type | Constraints | Description |
|--------|-----------|-------------|-------------|
| StudentID | INT | PK, FK, NOT NULL | References Students table |
| CourseID | INT | PK, FK, NOT NULL | References Courses table |
| EnrollmentDate | DATE | DEFAULT GETDATE() | When student enrolled |
| CreatedDate | DATETIME2 | DEFAULT GETDATE() | Record creation timestamp |

**Business Rules**:
- Composite primary key (StudentID, CourseID)
- Students can enroll in multiple courses
- Courses can have multiple students
- Enrollment date tracks when registration occurred

### **🟣 GRADES Table (Academic Results)**
**Purpose**: Stores final grades for student-course combinations

| Column | Data Type | Constraints | Description |
|--------|-----------|-------------|-------------|
| StudentID | INT | PK, FK, NOT NULL | References Students table |
| CourseID | INT | PK, FK, NOT NULL | References Courses table |
| Grade | DECIMAL(3,1) | NOT NULL, CHECK(0.0-4.0) | GPA scale grade |
| GradeDate | DATE | DEFAULT GETDATE() | When grade was assigned |
| CreatedDate | DATETIME2 | DEFAULT GETDATE() | Record creation timestamp |

**Business Rules**:
- Composite primary key (StudentID, CourseID)
- Uses standard 4.0 GPA scale (0.0 = F, 4.0 = A)
- Students can only have one final grade per course
- Not all enrollments have grades (courses in progress)

---

## 🔗 **Relationship Analysis**

### **1:N Relationships (One-to-Many)**

#### **DEPARTMENTS → LECTURERS** (1:N)
- **Cardinality**: One department employs many lecturers
- **Business Logic**: Faculty are organized by academic departments
- **Example**: Computer Science department has Dr. Anderson and Prof. Chen

#### **DEPARTMENTS → STUDENTS** (1:N)  
- **Cardinality**: One department has many students as majors
- **Business Logic**: Students declare a primary major department
- **Example**: Mathematics department has Sarah, Tom, and Emma as majors

#### **DEPARTMENTS → COURSES** (1:N)
- **Cardinality**: One department offers many courses
- **Business Logic**: Courses are organized by academic departments
- **Example**: Business department offers BUS101, BUS201, BUS301, BUS401

#### **LECTURERS → COURSES** (1:N)
- **Cardinality**: One lecturer teaches many courses
- **Business Logic**: Faculty members are assigned multiple courses
- **Example**: Dr. Anderson teaches both CS101 and CS201

### **N:M Relationships (Many-to-Many)**

#### **STUDENTS ↔ COURSES** (N:M via ENROLLMENTS)
- **Bridge Table**: ENROLLMENTS
- **Business Logic**: Students enroll in multiple courses; courses have multiple students
- **Example**: John (CS major) takes CS101, CS201, MATH101, and ENG101
- **Cross-Department**: Students can take courses outside their major

#### **STUDENTS ↔ COURSES** (N:M via GRADES)
- **Bridge Table**: GRADES  
- **Business Logic**: Students receive grades in courses they've completed
- **Example**: Sarah has grades in MATH101 (4.0), MATH201 (3.9), MATH301 (3.8)
- **Realistic Scenario**: Not all enrollments have grades (courses in progress)

---

## 💾 **Sample Data Overview**

### **Data Volume Summary**
- **5 Departments**: Computer Science, Mathematics, Business, English, Psychology
- **8 Lecturers**: Mix of professors and doctors across departments
- **12 Students**: Representing different enrollment years and majors
- **18 Courses**: 3-4 courses per department with realistic credit hours
- **35 Enrollments**: Cross-departmental enrollment patterns
- **20 Grades**: Mix of completed and in-progress courses

### **Realistic Business Scenarios**
1. **Cross-Departmental Learning**: CS students taking Math courses
2. **General Education**: All students taking English composition
3. **Electives and Minors**: English students taking Business courses
4. **Progressive Difficulty**: 101 (intro) → 201 → 301 → 401 (advanced)
5. **Incomplete Records**: Some enrollments without grades (realistic)

---

## 🎓 **Educational Benefits**

### **For SQL Beginners**
1. **Clear Relationships**: Easy to understand how tables connect
2. **Realistic Data**: University context is familiar and relatable
3. **Varied Queries**: Supports different types of SQL learning exercises
4. **Gradual Complexity**: Start simple, build to more complex operations

### **Learning Progression**
1. **Basic SELECT**: Query individual tables
2. **WHERE Clauses**: Filter data with conditions
3. **INNER JOINs**: Connect related tables
4. **OUTER JOINs**: Handle missing relationships
5. **GROUP BY**: Aggregate data for analysis
6. **Subqueries**: Advanced data retrieval

### **Real-World Applications**
- Student information systems
- Course registration platforms
- Academic reporting and analytics
- Grade management systems
- Faculty assignment tracking

---

## 🔧 **Technical Implementation**

### **SQL Server Features Used**
- **IDENTITY**: Auto-incrementing primary keys
- **NVARCHAR**: Unicode string support
- **DATETIME2**: High-precision timestamps
- **CHECK Constraints**: Data validation
- **FOREIGN KEY**: Referential integrity
- **DEFAULT Values**: Automatic field population

### **Best Practices Demonstrated**
- Proper naming conventions
- Comprehensive constraints
- Referential integrity maintenance
- Realistic sample data
- Clear documentation
- Educational progression

---

*This database serves as an excellent foundation for learning fundamental SQL concepts while working with realistic, relatable data scenarios.*
