-- =============================================
-- ADVANCED QUERY EXAMPLES - FUNDAMENTAL LEVEL
-- =============================================
-- Introduction to more advanced SQL concepts while keeping it beginner-friendly
-- Covers: Subqueries, Window Functions, CTEs, and Advanced JOINs

USE UNIVERSITY;

-- =============================================
-- 1. SUBQUERIES (Queries within Queries)
-- =============================================

-- Simple subquery: Find students in the largest department
SELECT 
    FirstName + ' ' + LastName AS StudentName,
    EnrollmentYear
FROM Students
WHERE DepartmentID = (
    SELECT TOP 1 DepartmentID
    FROM Students
    GROUP BY DepartmentID
    ORDER BY COUNT(*) DESC
);

-- Subquery with IN: Find students enrolled in Computer Science courses
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    S.Email
FROM Students S
WHERE S.StudentID IN (
    SELECT E.StudentID
    FROM Enrollments E
    JOIN Courses C ON E.CourseID = C.CourseID
    JOIN Departments D ON C.DepartmentID = D.DepartmentID
    WHERE D.Name = 'Computer Science'
);

-- Correlated subquery: Students with above-average GPA in their department
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    D.Name AS Department,
    (SELECT AVG(G.Grade) FROM Grades G WHERE G.StudentID = S.StudentID) AS StudentGPA
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
WHERE (
    SELECT AVG(G.Grade) 
    FROM Grades G 
    JOIN Students S2 ON G.StudentID = S2.StudentID
    WHERE S2.DepartmentID = S.DepartmentID
) < (
    SELECT AVG(G.Grade) 
    FROM Grades G 
    WHERE G.StudentID = S.StudentID
);

-- =============================================
-- 2. COMMON TABLE EXPRESSIONS (CTEs)
-- =============================================

-- Simple CTE: Calculate department statistics
WITH DepartmentStats AS (
    SELECT 
        D.DepartmentID,
        D.Name AS DepartmentName,
        COUNT(DISTINCT S.StudentID) AS StudentCount,
        COUNT(DISTINCT C.CourseID) AS CourseCount,
        AVG(G.Grade) AS AvgGPA
    FROM Departments D
    LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
    LEFT JOIN Courses C ON D.DepartmentID = C.DepartmentID
    LEFT JOIN Grades G ON S.StudentID = G.StudentID
    GROUP BY D.DepartmentID, D.Name
)
SELECT 
    DepartmentName,
    StudentCount,
    CourseCount,
    ROUND(AvgGPA, 2) AS AverageGPA,
    CASE 
        WHEN AvgGPA >= 3.5 THEN 'Excellent'
        WHEN AvgGPA >= 3.0 THEN 'Good'
        WHEN AvgGPA >= 2.5 THEN 'Average'
        ELSE 'Needs Improvement'
    END AS Performance
FROM DepartmentStats
ORDER BY AvgGPA DESC;

-- Multiple CTEs: Student performance analysis
WITH StudentGrades AS (
    SELECT 
        S.StudentID,
        S.FirstName + ' ' + S.LastName AS StudentName,
        S.DepartmentID,
        COUNT(G.Grade) AS CompletedCourses,
        AVG(G.Grade) AS GPA
    FROM Students S
    LEFT JOIN Grades G ON S.StudentID = G.StudentID
    GROUP BY S.StudentID, S.FirstName, S.LastName, S.DepartmentID
),
DepartmentAvg AS (
    SELECT 
        DepartmentID,
        AVG(GPA) AS DeptAvgGPA
    FROM StudentGrades
    WHERE GPA IS NOT NULL
    GROUP BY DepartmentID
)
SELECT 
    SG.StudentName,
    D.Name AS Department,
    SG.CompletedCourses,
    ROUND(SG.GPA, 2) AS StudentGPA,
    ROUND(DA.DeptAvgGPA, 2) AS DepartmentAverage,
    CASE 
        WHEN SG.GPA > DA.DeptAvgGPA THEN 'Above Average'
        WHEN SG.GPA = DA.DeptAvgGPA THEN 'Average'
        WHEN SG.GPA < DA.DeptAvgGPA THEN 'Below Average'
        ELSE 'No Grades Yet'
    END AS Comparison
FROM StudentGrades SG
JOIN Departments D ON SG.DepartmentID = D.DepartmentID
LEFT JOIN DepartmentAvg DA ON SG.DepartmentID = DA.DepartmentID
ORDER BY SG.GPA DESC;

-- =============================================
-- 3. WINDOW FUNCTIONS (Advanced but Useful)
-- =============================================

-- ROW_NUMBER: Rank students by GPA
SELECT 
    ROW_NUMBER() OVER (ORDER BY AVG(G.Grade) DESC) AS Rank,
    S.FirstName + ' ' + S.LastName AS StudentName,
    D.Name AS Department,
    COUNT(G.Grade) AS CoursesCompleted,
    ROUND(AVG(G.Grade), 2) AS GPA
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
LEFT JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName, D.Name
HAVING COUNT(G.Grade) > 0
ORDER BY Rank;

-- RANK and DENSE_RANK: Handle ties in ranking
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    D.Name AS Department,
    ROUND(AVG(G.Grade), 2) AS GPA,
    RANK() OVER (ORDER BY AVG(G.Grade) DESC) AS Rank,
    DENSE_RANK() OVER (ORDER BY AVG(G.Grade) DESC) AS DenseRank,
    RANK() OVER (PARTITION BY D.DepartmentID ORDER BY AVG(G.Grade) DESC) AS DeptRank
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName, D.DepartmentID, D.Name
ORDER BY GPA DESC;

-- LAG and LEAD: Compare with previous/next values
WITH StudentGPAs AS (
    SELECT 
        S.FirstName + ' ' + S.LastName AS StudentName,
        ROUND(AVG(G.Grade), 2) AS GPA
    FROM Students S
    JOIN Grades G ON S.StudentID = G.StudentID
    GROUP BY S.StudentID, S.FirstName, S.LastName
)
SELECT 
    StudentName,
    GPA,
    LAG(GPA) OVER (ORDER BY GPA) AS PreviousGPA,
    LEAD(GPA) OVER (ORDER BY GPA) AS NextGPA,
    GPA - LAG(GPA) OVER (ORDER BY GPA) AS DifferenceFromPrevious
FROM StudentGPAs
ORDER BY GPA DESC;

-- =============================================
-- 4. ADVANCED JOIN TECHNIQUES
-- =============================================

-- FULL OUTER JOIN: Show all students and all courses (even unmatched)
SELECT 
    ISNULL(S.FirstName + ' ' + S.LastName, 'No Student') AS StudentName,
    ISNULL(C.Code + ' - ' + C.Name, 'No Course') AS CourseName,
    CASE 
        WHEN E.StudentID IS NOT NULL THEN 'Enrolled'
        ELSE 'Not Enrolled'
    END AS EnrollmentStatus
FROM Students S
FULL OUTER JOIN Enrollments E ON S.StudentID = E.StudentID
FULL OUTER JOIN Courses C ON E.CourseID = C.CourseID
ORDER BY StudentName, CourseName;

-- CROSS JOIN: All possible student-course combinations (Cartesian product)
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code + ' - ' + C.Name AS CourseName,
    CASE 
        WHEN E.StudentID IS NOT NULL THEN 'Currently Enrolled'
        ELSE 'Available for Enrollment'
    END AS Status
FROM Students S
CROSS JOIN Courses C
LEFT JOIN Enrollments E ON S.StudentID = E.StudentID AND C.CourseID = E.CourseID
WHERE S.DepartmentID = C.DepartmentID  -- Only show courses in student's department
ORDER BY StudentName, CourseName;

-- =============================================
-- 5. COMPLEX AGGREGATIONS
-- =============================================

-- ROLLUP: Hierarchical totals
SELECT 
    ISNULL(D.Name, 'TOTAL') AS Department,
    ISNULL(CAST(S.EnrollmentYear AS VARCHAR), 'All Years') AS EnrollmentYear,
    COUNT(S.StudentID) AS StudentCount,
    AVG(G.Grade) AS AverageGPA
FROM Departments D
RIGHT JOIN Students S ON D.DepartmentID = S.DepartmentID
LEFT JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY ROLLUP(D.Name, S.EnrollmentYear)
ORDER BY D.Name, S.EnrollmentYear;

-- CUBE: All possible combinations
SELECT 
    ISNULL(D.Name, 'All Departments') AS Department,
    ISNULL(CAST(C.Credits AS VARCHAR), 'All Credits') AS Credits,
    COUNT(E.StudentID) AS TotalEnrollments,
    COUNT(DISTINCT E.StudentID) AS UniqueStudents
FROM Departments D
JOIN Courses C ON D.DepartmentID = C.DepartmentID
LEFT JOIN Enrollments E ON C.CourseID = E.CourseID
GROUP BY CUBE(D.Name, C.Credits)
ORDER BY Department, Credits;

-- =============================================
-- 6. CONDITIONAL AGGREGATION
-- =============================================

-- Pivot-like behavior without PIVOT
SELECT 
    D.Name AS Department,
    COUNT(CASE WHEN S.EnrollmentYear = 2022 THEN 1 END) AS Students_2022,
    COUNT(CASE WHEN S.EnrollmentYear = 2023 THEN 1 END) AS Students_2023,
    COUNT(CASE WHEN G.Grade >= 3.7 THEN 1 END) AS A_Grades,
    COUNT(CASE WHEN G.Grade BETWEEN 3.0 AND 3.69 THEN 1 END) AS B_Grades,
    COUNT(CASE WHEN G.Grade < 3.0 THEN 1 END) AS Below_B_Grades
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
LEFT JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY D.DepartmentID, D.Name
ORDER BY D.Name;

-- =============================================
-- PRACTICE EXERCISES
-- =============================================

-- Exercise 1: Find students who are enrolled in more courses than the average
-- Exercise 2: Use a CTE to find the top 2 students in each department by GPA
-- Exercise 3: Create a window function to show running total of credits by student
-- Exercise 4: Write a subquery to find courses with no enrollments
-- Exercise 5: Use CASE statements to create a grade distribution report
