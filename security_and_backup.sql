-- =============================================
-- BASIC SECURITY AND DATA PROTECTION
-- =============================================
-- Introduction to database security, backup, and recovery concepts
-- Fundamental level - covers essential security practices

USE UNIVERSITY;

-- =============================================
-- 1. USER MANAGEMENT AND PERMISSIONS
-- =============================================

-- Create different types of users for the university system
-- Note: These commands require administrative privileges

-- Create logins (server level)
/*
CREATE LOGIN UniversityAdmin WITH PASSWORD = 'AdminPass123!';
CREATE LOGIN StudentUser WITH PASSWORD = 'StudentPass123!';
CREATE LOGIN LecturerUser WITH PASSWORD = 'LecturerPass123!';
CREATE LOGIN ReadOnlyUser WITH PASSWORD = 'ReadOnlyPass123!';
*/

-- Create database users (database level)
/*
CREATE USER UniversityAdmin FOR LOGIN UniversityAdmin;
CREATE USER StudentUser FOR LOGIN StudentUser;
CREATE USER LecturerUser FOR LOGIN LecturerUser;
CREATE USER ReadOnlyUser FOR LOGIN ReadOnlyUser;
*/

-- =============================================
-- 2. ROLE-BASED SECURITY
-- =============================================

-- Create custom roles for different user types
/*
CREATE ROLE StudentRole;
CREATE ROLE LecturerRole;
CREATE ROLE AdminRole;
*/

-- Grant permissions to roles
/*
-- Student Role: Can view their own data and enroll in courses
GRANT SELECT ON Students TO StudentRole;
GRANT SELECT ON Courses TO StudentRole;
GRANT SELECT ON Departments TO StudentRole;
GRANT SELECT ON Enrollments TO StudentRole;
GRANT SELECT ON Grades TO StudentRole;
GRANT EXECUTE ON sp_EnrollStudent TO StudentRole;

-- Lecturer Role: Can view all academic data and manage grades
GRANT SELECT ON Students TO LecturerRole;
GRANT SELECT ON Courses TO LecturerRole;
GRANT SELECT ON Departments TO LecturerRole;
GRANT SELECT ON Enrollments TO LecturerRole;
GRANT SELECT, INSERT, UPDATE ON Grades TO LecturerRole;
GRANT EXECUTE ON sp_AddGradeWithValidation TO LecturerRole;
GRANT EXECUTE ON sp_GenerateStudentReport TO LecturerRole;

-- Admin Role: Full control over the database
GRANT CONTROL ON SCHEMA::dbo TO AdminRole;

-- Add users to roles
ALTER ROLE StudentRole ADD MEMBER StudentUser;
ALTER ROLE LecturerRole ADD MEMBER LecturerUser;
ALTER ROLE AdminRole ADD MEMBER UniversityAdmin;
ALTER ROLE db_datareader ADD MEMBER ReadOnlyUser;
*/

-- =============================================
-- 3. ROW-LEVEL SECURITY (Advanced Concept)
-- =============================================

-- Create a security policy so students can only see their own data
-- This is an advanced concept but important for real applications

/*
-- Add a security function
CREATE FUNCTION fn_StudentSecurityPredicate(@StudentID INT)
RETURNS TABLE
WITH SCHEMABINDING
AS
RETURN SELECT 1 AS fn_StudentSecurityPredicate_result
WHERE @StudentID = CAST(SESSION_CONTEXT(N'StudentID') AS INT) 
   OR IS_MEMBER('LecturerRole') = 1 
   OR IS_MEMBER('AdminRole') = 1;

-- Create security policy
CREATE SECURITY POLICY StudentSecurityPolicy
ADD FILTER PREDICATE fn_StudentSecurityPredicate(StudentID) ON Students,
ADD FILTER PREDICATE fn_StudentSecurityPredicate(StudentID) ON Enrollments,
ADD FILTER PREDICATE fn_StudentSecurityPredicate(StudentID) ON Grades
WITH (STATE = ON);
*/

-- =============================================
-- 4. DATA ENCRYPTION (Basic Concepts)
-- =============================================

-- Example of encrypting sensitive data (like SSN if we had it)
-- This shows the concept but we'll use email as an example

-- Create a master key (required for encryption)
/*
CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'UniversityMasterKey123!';

-- Create a certificate
CREATE CERTIFICATE UniversityCert
WITH SUBJECT = 'University Data Protection Certificate';

-- Create a symmetric key
CREATE SYMMETRIC KEY UniversityKey
WITH ALGORITHM = AES_256
ENCRYPTION BY CERTIFICATE UniversityCert;
*/

-- Example of how to encrypt/decrypt data
/*
-- Open the key
OPEN SYMMETRIC KEY UniversityKey
DECRYPTION BY CERTIFICATE UniversityCert;

-- Insert encrypted data (example)
INSERT INTO Students (FirstName, LastName, Email, EnrollmentYear, DepartmentID)
VALUES ('John', 'Doe', 
        ENCRYPTBYKEY(KEY_GUID('UniversityKey'), '<EMAIL>'),
        2024, 1);

-- Query encrypted data
SELECT 
    FirstName,
    LastName,
    CONVERT(VARCHAR, DECRYPTBYKEY(Email)) AS DecryptedEmail
FROM Students
WHERE StudentID = SCOPE_IDENTITY();

-- Close the key
CLOSE SYMMETRIC KEY UniversityKey;
*/

-- =============================================
-- 5. BACKUP STRATEGIES
-- =============================================

-- Full database backup
/*
BACKUP DATABASE UNIVERSITY
TO DISK = 'C:\Backups\UNIVERSITY_Full.bak'
WITH FORMAT, INIT,
NAME = 'UNIVERSITY Full Database Backup',
DESCRIPTION = 'Full backup of University database';
*/

-- Differential backup (only changes since last full backup)
/*
BACKUP DATABASE UNIVERSITY
TO DISK = 'C:\Backups\UNIVERSITY_Diff.bak'
WITH DIFFERENTIAL,
NAME = 'UNIVERSITY Differential Backup',
DESCRIPTION = 'Differential backup of University database';
*/

-- Transaction log backup (for point-in-time recovery)
/*
BACKUP LOG UNIVERSITY
TO DISK = 'C:\Backups\UNIVERSITY_Log.trn'
WITH NAME = 'UNIVERSITY Log Backup',
DESCRIPTION = 'Transaction log backup of University database';
*/

-- =============================================
-- 6. RESTORE OPERATIONS
-- =============================================

-- Restore full database
/*
RESTORE DATABASE UNIVERSITY_RESTORED
FROM DISK = 'C:\Backups\UNIVERSITY_Full.bak'
WITH MOVE 'UNIVERSITY' TO 'C:\Data\UNIVERSITY_RESTORED.mdf',
     MOVE 'UNIVERSITY_Log' TO 'C:\Data\UNIVERSITY_RESTORED.ldf',
     REPLACE;
*/

-- Point-in-time restore
/*
-- First restore the full backup with NORECOVERY
RESTORE DATABASE UNIVERSITY_PIT
FROM DISK = 'C:\Backups\UNIVERSITY_Full.bak'
WITH NORECOVERY,
     MOVE 'UNIVERSITY' TO 'C:\Data\UNIVERSITY_PIT.mdf',
     MOVE 'UNIVERSITY_Log' TO 'C:\Data\UNIVERSITY_PIT.ldf';

-- Then restore the differential backup with NORECOVERY
RESTORE DATABASE UNIVERSITY_PIT
FROM DISK = 'C:\Backups\UNIVERSITY_Diff.bak'
WITH NORECOVERY;

-- Finally restore the log to a specific point in time
RESTORE LOG UNIVERSITY_PIT
FROM DISK = 'C:\Backups\UNIVERSITY_Log.trn'
WITH STOPAT = '2024-01-15 14:30:00';
*/

-- =============================================
-- 7. AUDIT AND MONITORING
-- =============================================

-- Create an audit to track data access
/*
CREATE SERVER AUDIT UniversityAudit
TO FILE (FILEPATH = 'C:\Audits\');

CREATE DATABASE AUDIT SPECIFICATION UniversityDatabaseAudit
FOR SERVER AUDIT UniversityAudit
ADD (SELECT, INSERT, UPDATE, DELETE ON Students BY public),
ADD (SELECT, INSERT, UPDATE, DELETE ON Grades BY public);

-- Enable the audit
ALTER SERVER AUDIT UniversityAudit WITH (STATE = ON);
ALTER DATABASE AUDIT SPECIFICATION UniversityDatabaseAudit WITH (STATE = ON);
*/

-- =============================================
-- 8. DATA INTEGRITY CHECKS
-- =============================================

-- Check database consistency
/*
DBCC CHECKDB('UNIVERSITY') WITH NO_INFOMSGS;
*/

-- Check specific table integrity
/*
DBCC CHECKTABLE('Students') WITH NO_INFOMSGS;
*/

-- Update statistics for better performance
/*
UPDATE STATISTICS Students;
UPDATE STATISTICS Courses;
UPDATE STATISTICS Enrollments;
UPDATE STATISTICS Grades;
*/

-- =============================================
-- 9. BASIC SECURITY VIEWS
-- =============================================

-- Create a view that shows only non-sensitive student information
CREATE VIEW vw_PublicStudentInfo AS
SELECT 
    StudentID,
    FirstName,
    LastName,
    EnrollmentYear,
    D.Name AS Department
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID;

-- Create a view for lecturers to see their students
CREATE VIEW vw_LecturerStudents AS
SELECT 
    S.StudentID,
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code AS CourseCode,
    C.Name AS CourseName,
    G.Grade,
    L.FirstName + ' ' + L.LastName AS LecturerName
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
JOIN Lecturers L ON C.LecturerID = L.LecturerID
LEFT JOIN Grades G ON S.StudentID = G.StudentID AND C.CourseID = G.CourseID;

-- =============================================
-- 10. SECURITY BEST PRACTICES CHECKLIST
-- =============================================

/*
SECURITY CHECKLIST FOR UNIVERSITY DATABASE:

✓ Use strong passwords for all accounts
✓ Implement role-based access control
✓ Grant minimum necessary permissions
✓ Regularly backup the database
✓ Test restore procedures
✓ Enable auditing for sensitive operations
✓ Use encryption for sensitive data
✓ Regularly update statistics and check integrity
✓ Monitor for unusual access patterns
✓ Keep SQL Server updated with security patches
✓ Use Windows Authentication when possible
✓ Disable unnecessary services and features
✓ Implement network security (firewalls, VPNs)
✓ Regular security assessments
✓ Document security procedures

BACKUP STRATEGY RECOMMENDATIONS:
- Full backup: Weekly
- Differential backup: Daily
- Transaction log backup: Every 15 minutes
- Test restores: Monthly
- Offsite backup storage: Required
*/

-- =============================================
-- USAGE EXAMPLES
-- =============================================

-- Example 1: Create a backup job
/*
EXEC sp_add_job 
    @job_name = 'University Daily Backup',
    @description = 'Daily backup of University database';
*/

-- Example 2: Check who has access to what
/*
SELECT 
    p.principal_id,
    p.name AS principal_name,
    p.type_desc AS principal_type,
    o.object_id,
    o.name AS object_name,
    pe.permission_name,
    pe.state_desc AS permission_state
FROM sys.database_permissions pe
LEFT JOIN sys.objects o ON pe.major_id = o.object_id
LEFT JOIN sys.database_principals p ON pe.grantee_principal_id = p.principal_id
WHERE p.name IS NOT NULL
ORDER BY p.name, o.name;
*/
