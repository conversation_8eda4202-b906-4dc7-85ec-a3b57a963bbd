-- =============================================
-- UNIVERSITY COURSE MANAGEMENT SYSTEM DATABASE
-- =============================================

CREATE DATABASE UniversityCourseManagement;
USE UniversityCourseManagement;

-- =============================================
-- 1. TABLES CREATION CODES
-- =============================================

-- DEPARTMENTS Table
CREATE TABLE Departments (
    DepartmentID INT PRIMARY KEY IDENTITY,
    Name NVARCHAR(100) NOT NULL,
    Building NVARCHAR(50) NOT NULL
);

-- LECTURERS Table
CREATE TABLE Lecturers (
    LecturerID INT PRIMARY KEY IDENTITY,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100) NOT NULL,
    DepartmentID INT NOT NULL,
    FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID)
);

-- STUDENTS Table
CREATE TABLE Students (
    StudentID INT PRIMARY KEY IDENTITY,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100) NOT NULL,
    EnrollmentYear INT NOT NULL,
    DepartmentID INT NOT NULL,
    FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID)
);

-- COURSES Table
CREATE TABLE Courses (
    CourseID INT PRIMARY KEY IDENTITY,
    Code NVARCHAR(10) NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    Credits INT NOT NULL,
    DepartmentID INT NOT NULL,
    LecturerID INT NOT NULL,
    FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID),
    FOREIGN KEY (LecturerID) REFERENCES Lecturers(LecturerID)
);

-- ENROLLMENTS Table (Many-to-Many Bridge)
CREATE TABLE Enrollments (
    StudentID INT NOT NULL,
    CourseID INT NOT NULL,
    EnrollmentDate DATE DEFAULT GETDATE(),
    PRIMARY KEY (StudentID, CourseID),
    FOREIGN KEY (StudentID) REFERENCES Students(StudentID),
    FOREIGN KEY (CourseID) REFERENCES Courses(CourseID)
);

-- GRADES Table
CREATE TABLE Grades (
    StudentID INT NOT NULL,
    CourseID INT NOT NULL,
    Grade DECIMAL(3,1) NOT NULL,
    GradeDate DATE DEFAULT GETDATE(),
    PRIMARY KEY (StudentID, CourseID),
    FOREIGN KEY (StudentID) REFERENCES Students(StudentID),
    FOREIGN KEY (CourseID) REFERENCES Courses(CourseID)
);

-- =============================================
-- 2. VALUES ENTERING CODES (INSERTS)
-- =============================================

-- Departments
INSERT INTO Departments (Name, Building) VALUES
('Computer Science', 'Technology Center'),
('Mathematics', 'Science Hall'),
('Business Administration', 'Business Complex'),
('English Literature', 'Humanities Building'),
('Psychology', 'Social Sciences Wing');

-- Lecturers
INSERT INTO Lecturers (FirstName, LastName, Email, DepartmentID) VALUES
('Dr. Robert', 'Anderson', '<EMAIL>', 1),
('Prof. Jennifer', 'Chen', '<EMAIL>', 1),
('Prof. Mary', 'Taylor', '<EMAIL>', 2),
('Dr. David', 'Kumar', '<EMAIL>', 2),
('Dr. James', 'Miller', '<EMAIL>', 3),
('Prof. Susan', 'Williams', '<EMAIL>', 3),
('Dr. Emily', 'Johnson', '<EMAIL>', 4),
('Prof. Michael', 'Davis', '<EMAIL>', 5);

-- Students
INSERT INTO Students (FirstName, LastName, Email, EnrollmentYear, DepartmentID) VALUES
('John', 'Smith', '<EMAIL>', 2023, 1),
('Mike', 'Brown', '<EMAIL>', 2022, 1),
('Alex', 'Rodriguez', '<EMAIL>', 2024, 1),
('Sarah', 'Johnson', '<EMAIL>', 2022, 2),
('Tom', 'Wilson', '<EMAIL>', 2023, 2),
('Emma', 'Thompson', '<EMAIL>', 2024, 2),
('Lisa', 'Davis', '<EMAIL>', 2022, 3),
('Anna', 'Garcia', '<EMAIL>', 2023, 3),
('Rachel', 'Martinez', '<EMAIL>', 2023, 4),
('Kevin', 'Lee', '<EMAIL>', 2022, 4),
('Jessica', 'White', '<EMAIL>', 2024, 5),
('Daniel', 'Clark', '<EMAIL>', 2023, 5);

-- Courses
INSERT INTO Courses (Code, Name, Credits, DepartmentID, LecturerID) VALUES
('CS101', 'Introduction to Programming', 3, 1, 1),
('CS201', 'Data Structures and Algorithms', 4, 1, 1),
('CS301', 'Database Systems', 3, 1, 2),
('CS401', 'Software Engineering', 4, 1, 2),
('MATH101', 'Calculus I', 4, 2, 3),
('MATH201', 'Calculus II', 4, 2, 3),
('MATH301', 'Statistics and Probability', 3, 2, 4),
('MATH401', 'Linear Algebra', 3, 2, 4),
('BUS101', 'Business Fundamentals', 3, 3, 5),
('BUS201', 'Marketing Principles', 3, 3, 5),
('BUS301', 'Financial Management', 4, 3, 6),
('BUS401', 'Strategic Management', 3, 3, 6),
('ENG101', 'Composition and Rhetoric', 3, 4, 7),
('ENG201', 'World Literature', 3, 4, 7),
('ENG301', 'American Literature', 3, 4, 7),
('PSY101', 'Introduction to Psychology', 3, 5, 8),
('PSY201', 'Developmental Psychology', 3, 5, 8),
('PSY301', 'Cognitive Psychology', 4, 5, 8);

-- Enrollments
INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate) VALUES
-- John Smith (CS Student) - CS and Math courses
(1, 1, '2023-08-28'), -- CS101
(1, 2, '2023-08-28'), -- CS201
(1, 5, '2023-08-28'), -- MATH101
(1, 13, '2023-08-28'), -- ENG101

-- Mike Brown (CS Student) - Advanced CS
(2, 2, '2022-08-29'), -- CS201
(2, 3, '2022-08-29'), -- CS301
(2, 6, '2022-08-29'), -- MATH201
(2, 7, '2022-08-29'), -- MATH301

-- Alex Rodriguez (CS Student) - New student
(3, 1, '2024-08-26'), -- CS101
(3, 5, '2024-08-26'), -- MATH101
(3, 13, '2024-08-26'), -- ENG101

-- Sarah Johnson (Math Student)
(4, 5, '2022-08-29'), -- MATH101
(4, 6, '2022-08-29'), -- MATH201
(4, 7, '2022-08-29'), -- MATH301
(4, 1, '2022-08-29'), -- CS101

-- Tom Wilson (Math Student)
(5, 6, '2023-08-28'), -- MATH201
(5, 8, '2023-08-28'), -- MATH401
(5, 16, '2023-08-28'), -- PSY101

-- Emma Thompson (Math Student)
(6, 5, '2024-08-26'), -- MATH101
(6, 13, '2024-08-26'), -- ENG101

-- Lisa Davis (Business Student)
(7, 9, '2022-08-29'), -- BUS101
(7, 10, '2022-08-29'), -- BUS201
(7, 11, '2022-08-29'), -- BUS301
(7, 7, '2022-08-29'), -- MATH301

-- Anna Garcia (Business Student)
(8, 10, '2023-08-28'), -- BUS201
(8, 12, '2023-08-28'), -- BUS401
(8, 14, '2023-08-28'), -- ENG201

-- Rachel Martinez (English Student)
(9, 13, '2023-08-28'), -- ENG101
(9, 14, '2023-08-28'), -- ENG201
(9, 16, '2023-08-28'), -- PSY101

-- Kevin Lee (English Student)
(10, 14, '2022-08-29'), -- ENG201
(10, 15, '2022-08-29'), -- ENG301
(10, 9, '2022-08-29'), -- BUS101

-- Jessica White (Psychology Student)
(11, 16, '2024-08-26'), -- PSY101
(11, 13, '2024-08-26'), -- ENG101
(11, 5, '2024-08-26'), -- MATH101

-- Daniel Clark (Psychology Student)
(12, 17, '2023-08-28'), -- PSY201
(12, 18, '2023-08-28'), -- PSY301
(12, 7, '2023-08-28'); -- MATH301

-- Grades
INSERT INTO Grades (StudentID, CourseID, Grade, GradeDate) VALUES
-- Completed courses with grades
(1, 1, 3.7, '2023-12-15'), -- John: CS101 B+
(1, 5, 3.3, '2023-12-15'), -- John: MATH101 B
(2, 2, 3.0, '2022-12-16'), -- Mike: CS201 B-
(2, 3, 3.8, '2022-12-16'), -- Mike: CS301 A-
(2, 6, 3.5, '2022-12-16'), -- Mike: MATH201 B+
(2, 7, 4.0, '2022-12-16'), -- Mike: MATH301 A
(4, 5, 4.0, '2022-12-16'), -- Sarah: MATH101 A
(4, 6, 3.9, '2022-12-16'), -- Sarah: MATH201 A-
(4, 7, 3.8, '2022-12-16'), -- Sarah: MATH301 A-
(4, 1, 3.2, '2022-12-16'), -- Sarah: CS101 B
(5, 6, 3.4, '2023-12-15'), -- Tom: MATH201 B+
(7, 9, 3.6, '2022-12-16'), -- Lisa: BUS101 B+
(7, 10, 3.3, '2022-12-16'), -- Lisa: BUS201 B
(7, 11, 3.9, '2022-12-16'), -- Lisa: BUS301 A-
(7, 7, 3.1, '2022-12-16'), -- Lisa: MATH301 B-
(8, 10, 3.5, '2023-12-15'), -- Anna: BUS201 B+
(10, 14, 3.8, '2022-12-16'), -- Kevin: ENG201 A-
(10, 15, 4.0, '2022-12-16'), -- Kevin: ENG301 A
(10, 9, 3.0, '2022-12-16'), -- Kevin: BUS101 B-
(12, 17, 3.7, '2023-12-15'), -- Daniel: PSY201 B+
(1, 2, 3.4, '2024-05-15'), -- John: CS201 B+ (spring semester)
(3, 1, 2.8, '2024-12-15'), -- Alex: CS101 C+
(9, 13, 3.6, '2023-12-15'), -- Rachel: ENG101 B+
(11, 16, 3.2, '2024-12-15'); -- Jessica: PSY101 B



PRINT 'University Course Management System database created successfully!';
PRINT 'Tables: 6 (Departments, Lecturers, Students, Courses, Enrollments, Grades)';
PRINT 'Sample Data: 5 Departments, 8 Lecturers, 12 Students, 18 Courses';
PRINT 'Enrollments: 35 student-course registrations';
PRINT 'Grades: 24 completed course grades';
PRINT 'Ready for SQL learning exercises!';
