-- Sample Data for University Course Management System
USE UNIVERSITY;

-- Insert Departments
INSERT INTO Departments (Name, Building) VALUES
('Computer Science', 'A1'),
('Mathematics', 'B2'),
('Physics', 'C3'),
('Chemistry', 'D4'),
('Biology', 'E5');

-- Insert Lecturers
INSERT INTO Lecturers (FirstName, LastName, Title, DepartmentID) VALUES
('<PERSON>', '<PERSON><PERSON><PERSON>', 'Dr', 1),
('<PERSON>', '<PERSON><PERSON>', 'Prof.', 2),
('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Dr', 3),
('<PERSON>ot<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Dr', 4),
('<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Prof.', 5),
('<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Dr', 1),
('<PERSON><PERSON><PERSON><PERSON>', 'Wójci<PERSON>', 'Dr', 2);

-- Insert Students
INSERT INTO Students (FirstName, LastName, EnrollmentYear, DepartmentID) VALUES
('<PERSON><PERSON><PERSON>', 'Lis', 2022, 1),
('<PERSON>', '<PERSON><PERSON>czyk', 2023, 2),
('<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 2022, 3),
('<PERSON><PERSON>', '<PERSON><PERSON>', 2023, 1),
('<PERSON><PERSON><PERSON>', '<PERSON>walski', 2021, 4),
('<PERSON>', 'Szymańska', 2022, 5),
('Paweł', 'Jankowski', 2023, 2),
('Magdalena', 'Mazur', 2021, 3);

-- Insert Courses
INSERT INTO Courses (Code, Name, Credits, DepartmentID, LecturerID) VALUES
('CS101', 'Introduction to Programming', 5, 1, 1),
('CS201', 'Database Systems', 5, 1, 6),
('MATH101', 'Calculus I', 4, 2, 2),
('MATH201', 'Linear Algebra', 4, 2, 7),
('PHY101', 'General Physics', 6, 3, 3),
('PHY301', 'Quantum Mechanics', 6, 3, 3),
('CHEM101', 'General Chemistry', 5, 4, 4),
('BIO101', 'General Biology', 4, 5, 5);

-- Insert Enrollments
INSERT INTO Enrollments (StudentID, CourseID) VALUES
(1, 1), -- Mateusz in CS101
(1, 2), -- Mateusz in CS201
(1, 3), -- Mateusz in MATH101
(2, 3), -- Julia in MATH101
(2, 4), -- Julia in MATH201
(3, 5), -- Oskar in PHY101
(3, 6), -- Oskar in PHY301
(4, 1), -- Aleksandra in CS101
(4, 2), -- Aleksandra in CS201
(5, 7), -- Michał in CHEM101
(6, 8), -- Natalia in BIO101
(7, 3), -- Paweł in MATH101
(7, 4), -- Paweł in MATH201
(8, 5), -- Magdalena in PHY101
(8, 6); -- Magdalena in PHY301

-- Insert Grades
INSERT INTO Grades (StudentID, CourseID, Grade) VALUES
(1, 1, 4.5), -- Mateusz CS101
(1, 3, 4.0), -- Mateusz MATH101
(2, 3, 5.0), -- Julia MATH101
(2, 4, 4.5), -- Julia MATH201
(3, 5, 3.5), -- Oskar PHY101
(4, 1, 4.0), -- Aleksandra CS101
(5, 7, 3.0), -- Michał CHEM101
(6, 8, 4.5), -- Natalia BIO101
(7, 3, 3.5), -- Paweł MATH101
(8, 5, 4.0); -- Magdalena PHY101

-- Insert Course Schedule
INSERT INTO CourseSchedule (CourseID, Room, DayOfWeek, StartTime, EndTime) VALUES
(1, '101', 'Monday', '09:00', '11:00'),
(1, '101', 'Wednesday', '09:00', '11:00'),
(2, '102', 'Tuesday', '10:00', '12:00'),
(2, '102', 'Thursday', '10:00', '12:00'),
(3, '201', 'Monday', '13:00', '15:00'),
(3, '201', 'Friday', '13:00', '15:00'),
(4, '202', 'Tuesday', '14:00', '16:00'),
(4, '202', 'Thursday', '14:00', '16:00'),
(5, '301', 'Wednesday', '08:00', '10:00'),
(5, '301', 'Friday', '08:00', '10:00'),
(6, '302', 'Monday', '15:00', '17:00'),
(6, '302', 'Wednesday', '15:00', '17:00'),
(7, '401', 'Tuesday', '11:00', '13:00'),
(7, '401', 'Thursday', '11:00', '13:00'),
(8, '501', 'Monday', '10:00', '12:00'),
(8, '501', 'Friday', '10:00', '12:00');
