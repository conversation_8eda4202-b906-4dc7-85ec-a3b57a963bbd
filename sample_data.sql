-- =============================================
-- BASIC SAMPLE DATA - FUNDAMENTAL LEVEL
-- =============================================
-- Simple, easy-to-understand data for learning basic SQL operations

USE UNIVERSITY;

-- Insert Departments (Keep it simple - 3 departments)
INSERT INTO Departments (Name, Building) VALUES
('Computer Science', 'Tech Building'),
('Mathematics', 'Science Hall'),
('Business', 'Admin Building');

-- Insert Students (6 students - easy to track)
INSERT INTO Students (FirstName, LastName, Email, EnrollmentYear, DepartmentID) VALUES
('<PERSON>', '<PERSON>', '<EMAIL>', 2023, 1),
('<PERSON>', '<PERSON>', '<EMAIL>', 2022, 2),
('<PERSON>', '<PERSON>', '<EMAIL>', 2023, 1),
('<PERSON>', '<PERSON>', '<EMAIL>', 2022, 3),
('<PERSON>', '<PERSON>', '<EMAIL>', 2023, 2),
('<PERSON>', '<PERSON>', '<EMAIL>', 2022, 3);

-- Insert Lecturers (3 lecturers - one per department)
INSERT INTO Lecturers (First<PERSON>ame, LastName, Email, DepartmentID) VALUES
('Dr. <PERSON>', 'Anderson', '<EMAIL>', 1),
('Prof. Mary', 'Taylor', '<EMAIL>', 2),
('Dr. James', 'Miller', '<EMAIL>', 3);

-- Insert Courses (6 courses - 2 per department)
INSERT INTO Courses (Code, Name, Credits, DepartmentID, LecturerID) VALUES
('CS101', 'Introduction to Programming', 3, 1, 1),
('CS201', 'Data Structures', 4, 1, 1),
('MATH101', 'Calculus I', 4, 2, 2),
('MATH201', 'Statistics', 3, 2, 2),
('BUS101', 'Business Fundamentals', 3, 3, 3),
('BUS201', 'Marketing Basics', 3, 3, 3);

-- Insert Enrollments (Simple enrollment pattern)
INSERT INTO Enrollments (StudentID, CourseID) VALUES
-- John (CS student) takes CS and Math courses
(1, 1), -- John in CS101
(1, 3), -- John in MATH101
-- Sarah (Math student) takes Math courses
(2, 3), -- Sarah in MATH101
(2, 4), -- Sarah in MATH201
-- Mike (CS student) takes CS courses
(3, 1), -- Mike in CS101
(3, 2), -- Mike in CS201
-- Lisa (Business student) takes Business courses
(4, 5), -- Lisa in BUS101
(4, 6), -- Lisa in BUS201
-- Tom (Math student) takes Math courses
(5, 3), -- Tom in MATH101
(5, 4), -- Tom in MATH201
-- Anna (Business student) takes Business and Math
(6, 5), -- Anna in BUS101
(6, 3); -- Anna in MATH101

-- Insert Grades (Some students have grades, others don't - for learning purposes)
INSERT INTO Grades (StudentID, CourseID, Grade) VALUES
(1, 1, 3.7), -- John got B+ in CS101
(1, 3, 3.3), -- John got B in MATH101
(2, 3, 4.0), -- Sarah got A in MATH101
(3, 1, 3.0), -- Mike got B- in CS101
(4, 5, 3.5), -- Lisa got B+ in BUS101
(5, 3, 2.7); -- Tom got C+ in MATH101
-- Note: Some enrollments don't have grades yet (realistic scenario)
