-- =============================================
-- COMPREHENSIVE SAMPLE DATA FOR UNIVERSITY COURSE MANAGEMENT SYSTEM
-- =============================================
-- Realistic, meaningful sample data demonstrating all relationships
-- Designed for fundamental SQL learning with proper referential integrity
-- Compatible with SQL Server Management Studio (SSMS)

USE UNIVERSITY;
GO

-- =============================================
-- CLEAR EXISTING DATA (for clean re-runs)
-- =============================================
-- Delete in reverse order of dependencies to maintain referential integrity
DELETE FROM Grades;
DELETE FROM Enrollments;
DELETE FROM Courses;
DELETE FROM Lecturers;
DELETE FROM Students;
DELETE FROM Departments;

-- Reset IDENTITY counters
DBCC CHECKIDENT ('Grades', RESEED, 0);
DBCC CHECKIDENT ('Enrollments', RESEED, 0);
DBCC CHECKIDENT ('Courses', RESEED, 0);
DBCC CHECKIDENT ('Lecturers', RESEED, 0);
DBCC CHECKIDENT ('Students', RESEED, 0);
DBCC CHECKIDENT ('Departments', RESEED, 0);

-- =============================================
-- DEPARTMENTS DATA
-- =============================================
-- Purpose: Academic departments that organize courses and faculty
-- Business Context: Typical university departments with realistic building assignments

INSERT INTO Departments (Name, Building) VALUES
('Computer Science', 'Technology Center'),
('Mathematics', 'Science Hall'),
('Business Administration', 'Business Complex'),
('English Literature', 'Humanities Building'),
('Psychology', 'Social Sciences Wing');

PRINT 'Departments inserted: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =============================================
-- LECTURERS DATA
-- =============================================
-- Purpose: Faculty members teaching courses in their respective departments
-- Business Context: Mix of professors, associate professors, and assistant professors

INSERT INTO Lecturers (FirstName, LastName, Email, DepartmentID, HireDate) VALUES
-- Computer Science Department (ID: 1)
('Dr. Robert', 'Anderson', '<EMAIL>', 1, '2018-08-15'),
('Prof. Jennifer', 'Chen', '<EMAIL>', 1, '2020-01-10'),

-- Mathematics Department (ID: 2)
('Prof. Mary', 'Taylor', '<EMAIL>', 2, '2015-09-01'),
('Dr. David', 'Kumar', '<EMAIL>', 2, '2019-08-20'),

-- Business Administration Department (ID: 3)
('Dr. James', 'Miller', '<EMAIL>', 3, '2017-01-15'),
('Prof. Susan', 'Williams', '<EMAIL>', 3, '2021-08-25'),

-- English Literature Department (ID: 4)
('Dr. Emily', 'Johnson', '<EMAIL>', 4, '2016-09-10'),

-- Psychology Department (ID: 5)
('Prof. Michael', 'Davis', '<EMAIL>', 5, '2019-01-08');

PRINT 'Lecturers inserted: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =============================================
-- STUDENTS DATA
-- =============================================
-- Purpose: Student body representing different departments and enrollment years
-- Business Context: Mix of students from different years and departments

INSERT INTO Students (FirstName, LastName, Email, EnrollmentYear, DepartmentID) VALUES
-- Computer Science Students
('John', 'Smith', '<EMAIL>', 2023, 1),
('Mike', 'Brown', '<EMAIL>', 2022, 1),
('Alex', 'Rodriguez', '<EMAIL>', 2024, 1),

-- Mathematics Students
('Sarah', 'Johnson', '<EMAIL>', 2022, 2),
('Tom', 'Wilson', '<EMAIL>', 2023, 2),
('Emma', 'Thompson', '<EMAIL>', 2024, 2),

-- Business Administration Students
('Lisa', 'Davis', '<EMAIL>', 2022, 3),
('Anna', 'Garcia', '<EMAIL>', 2023, 3),

-- English Literature Students
('Rachel', 'Martinez', '<EMAIL>', 2023, 4),
('Kevin', 'Lee', '<EMAIL>', 2022, 4),

-- Psychology Students
('Jessica', 'White', '<EMAIL>', 2024, 5),
('Daniel', 'Clark', '<EMAIL>', 2023, 5);

PRINT 'Students inserted: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =============================================
-- COURSES DATA
-- =============================================
-- Purpose: Academic courses offered by each department
-- Business Context: Mix of introductory and advanced courses with appropriate credit hours

INSERT INTO Courses (Code, Name, Credits, DepartmentID, LecturerID) VALUES
-- Computer Science Courses (Department ID: 1)
('CS101', 'Introduction to Programming', 3, 1, 1),    -- Dr. Anderson
('CS201', 'Data Structures and Algorithms', 4, 1, 1), -- Dr. Anderson
('CS301', 'Database Systems', 3, 1, 2),               -- Prof. Chen
('CS401', 'Software Engineering', 4, 1, 2),           -- Prof. Chen

-- Mathematics Courses (Department ID: 2)
('MATH101', 'Calculus I', 4, 2, 3),                   -- Prof. Taylor
('MATH201', 'Calculus II', 4, 2, 3),                  -- Prof. Taylor
('MATH301', 'Statistics and Probability', 3, 2, 4),   -- Dr. Kumar
('MATH401', 'Linear Algebra', 3, 2, 4),               -- Dr. Kumar

-- Business Administration Courses (Department ID: 3)
('BUS101', 'Business Fundamentals', 3, 3, 5),         -- Dr. Miller
('BUS201', 'Marketing Principles', 3, 3, 5),          -- Dr. Miller
('BUS301', 'Financial Management', 4, 3, 6),          -- Prof. Williams
('BUS401', 'Strategic Management', 3, 3, 6),          -- Prof. Williams

-- English Literature Courses (Department ID: 4)
('ENG101', 'Composition and Rhetoric', 3, 4, 7),      -- Dr. Johnson
('ENG201', 'World Literature', 3, 4, 7),              -- Dr. Johnson
('ENG301', 'American Literature', 3, 4, 7),           -- Dr. Johnson

-- Psychology Courses (Department ID: 5)
('PSY101', 'Introduction to Psychology', 3, 5, 8),    -- Prof. Davis
('PSY201', 'Developmental Psychology', 3, 5, 8),      -- Prof. Davis
('PSY301', 'Cognitive Psychology', 4, 5, 8);          -- Prof. Davis

PRINT 'Courses inserted: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =============================================
-- ENROLLMENTS DATA
-- =============================================
-- Purpose: Student course registrations demonstrating realistic enrollment patterns
-- Business Context: Students take courses within and outside their major departments

INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate) VALUES
-- John Smith (CS Student, ID: 1) - Taking CS and Math courses
(1, 1, '2023-08-28'),  -- CS101: Introduction to Programming
(1, 2, '2023-08-28'),  -- CS201: Data Structures
(1, 5, '2023-08-28'),  -- MATH101: Calculus I
(1, 14, '2023-08-28'), -- ENG101: Composition (General Education)

-- Mike Brown (CS Student, ID: 2) - Advanced CS student
(2, 2, '2022-08-29'),  -- CS201: Data Structures
(2, 3, '2022-08-29'),  -- CS301: Database Systems
(2, 6, '2022-08-29'),  -- MATH201: Calculus II
(2, 7, '2022-08-29'),  -- MATH301: Statistics

-- Alex Rodriguez (CS Student, ID: 3) - New student
(3, 1, '2024-08-26'),  -- CS101: Introduction to Programming
(3, 5, '2024-08-26'),  -- MATH101: Calculus I
(3, 14, '2024-08-26'), -- ENG101: Composition

-- Sarah Johnson (Math Student, ID: 4) - Math major
(4, 5, '2022-08-29'),  -- MATH101: Calculus I
(4, 6, '2022-08-29'),  -- MATH201: Calculus II
(4, 7, '2022-08-29'),  -- MATH301: Statistics
(4, 1, '2022-08-29'),  -- CS101: Programming (Elective)

-- Tom Wilson (Math Student, ID: 5) - Current Math student
(5, 6, '2023-08-28'),  -- MATH201: Calculus II
(5, 8, '2023-08-28'),  -- MATH401: Linear Algebra
(5, 17, '2023-08-28'), -- PSY101: Psychology (Elective)

-- Emma Thompson (Math Student, ID: 6) - New Math student
(6, 5, '2024-08-26'),  -- MATH101: Calculus I
(6, 14, '2024-08-26'), -- ENG101: Composition

-- Lisa Davis (Business Student, ID: 7) - Business major
(7, 9, '2022-08-29'),  -- BUS101: Business Fundamentals
(7, 10, '2022-08-29'), -- BUS201: Marketing Principles
(7, 11, '2022-08-29'), -- BUS301: Financial Management
(7, 7, '2022-08-29'),  -- MATH301: Statistics (Required)

-- Anna Garcia (Business Student, ID: 8) - Current Business student
(8, 10, '2023-08-28'), -- BUS201: Marketing Principles
(8, 12, '2023-08-28'), -- BUS401: Strategic Management
(8, 15, '2023-08-28'), -- ENG201: World Literature

-- Rachel Martinez (English Student, ID: 9) - English major
(9, 14, '2023-08-28'), -- ENG101: Composition
(9, 15, '2023-08-28'), -- ENG201: World Literature
(9, 17, '2023-08-28'), -- PSY101: Psychology (Elective)

-- Kevin Lee (English Student, ID: 10) - Advanced English student
(10, 15, '2022-08-29'), -- ENG201: World Literature
(10, 16, '2022-08-29'), -- ENG301: American Literature
(10, 9, '2022-08-29'),  -- BUS101: Business (Minor)

-- Jessica White (Psychology Student, ID: 11) - New Psychology student
(11, 17, '2024-08-26'), -- PSY101: Introduction to Psychology
(11, 14, '2024-08-26'), -- ENG101: Composition
(11, 5, '2024-08-26'),  -- MATH101: Calculus I

-- Daniel Clark (Psychology Student, ID: 12) - Current Psychology student
(12, 18, '2023-08-28'), -- PSY201: Developmental Psychology
(12, 19, '2023-08-28'), -- PSY301: Cognitive Psychology
(12, 7, '2023-08-28');  -- MATH301: Statistics (Required)

PRINT 'Enrollments inserted: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
-- =============================================
-- GRADES DATA
-- =============================================
-- Purpose: Final grades for completed courses
-- Business Context: Mix of completed and in-progress courses (realistic scenario)
-- Note: Not all enrollments have grades yet - some courses are still in progress

INSERT INTO Grades (StudentID, CourseID, Grade, GradeDate) VALUES
-- John Smith (CS Student, ID: 1) - Completed courses from previous semester
(1, 1, 3.7, '2023-12-15'), -- CS101: B+ (3.7)
(1, 5, 3.3, '2023-12-15'), -- MATH101: B (3.3)
-- Note: CS201 and ENG101 still in progress (current semester)

-- Mike Brown (CS Student, ID: 2) - Advanced student with completed courses
(2, 2, 3.0, '2022-12-16'), -- CS201: B- (3.0)
(2, 3, 3.8, '2022-12-16'), -- CS301: A- (3.8)
(2, 6, 3.5, '2022-12-16'), -- MATH201: B+ (3.5)
(2, 7, 4.0, '2022-12-16'), -- MATH301: A (4.0)

-- Sarah Johnson (Math Student, ID: 4) - Strong math performance
(4, 5, 4.0, '2022-12-16'), -- MATH101: A (4.0)
(4, 6, 3.9, '2022-12-16'), -- MATH201: A- (3.9)
(4, 7, 3.8, '2022-12-16'), -- MATH301: A- (3.8)
(4, 1, 3.2, '2022-12-16'), -- CS101: B (3.2) - Elective

-- Tom Wilson (Math Student, ID: 5) - Some completed courses
(5, 6, 3.4, '2023-12-15'), -- MATH201: B+ (3.4)
-- Note: MATH401 and PSY101 still in progress

-- Lisa Davis (Business Student, ID: 7) - Business major performance
(7, 9, 3.6, '2022-12-16'),  -- BUS101: B+ (3.6)
(7, 10, 3.3, '2022-12-16'), -- BUS201: B (3.3)
(7, 11, 3.9, '2022-12-16'), -- BUS301: A- (3.9)
(7, 7, 3.1, '2022-12-16'),  -- MATH301: B- (3.1) - Required course

-- Anna Garcia (Business Student, ID: 8) - Some completed work
(8, 10, 3.5, '2023-12-15'), -- BUS201: B+ (3.5)
-- Note: BUS401 and ENG201 still in progress

-- Kevin Lee (English Student, ID: 10) - Literature focus
(10, 15, 3.8, '2022-12-16'), -- ENG201: A- (3.8)
(10, 16, 4.0, '2022-12-16'), -- ENG301: A (4.0)
(10, 9, 3.0, '2022-12-16'),  -- BUS101: B- (3.0) - Minor

-- Daniel Clark (Psychology Student, ID: 12) - Psychology performance
(12, 18, 3.7, '2023-12-15'), -- PSY201: B+ (3.7)
-- Note: PSY301 and MATH301 still in progress

-- Additional grades for variety in GPA calculations
(1, 2, 3.4, '2024-05-15'),   -- John's CS201: B+ (completed in spring)
(3, 1, 2.8, '2024-12-15'),   -- Alex's CS101: C+ (first semester grade)
(9, 14, 3.6, '2023-12-15'),  -- Rachel's ENG101: B+
(11, 17, 3.2, '2024-12-15'); -- Jessica's PSY101: B (completed first semester)

PRINT 'Grades inserted: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- =============================================
-- DATA SUMMARY AND VERIFICATION
-- =============================================
PRINT '';
PRINT '=== DATABASE POPULATION SUMMARY ===';
PRINT 'Departments: ' + CAST((SELECT COUNT(*) FROM Departments) AS NVARCHAR(10));
PRINT 'Lecturers: ' + CAST((SELECT COUNT(*) FROM Lecturers) AS NVARCHAR(10));
PRINT 'Students: ' + CAST((SELECT COUNT(*) FROM Students) AS NVARCHAR(10));
PRINT 'Courses: ' + CAST((SELECT COUNT(*) FROM Courses) AS NVARCHAR(10));
PRINT 'Enrollments: ' + CAST((SELECT COUNT(*) FROM Enrollments) AS NVARCHAR(10));
PRINT 'Grades: ' + CAST((SELECT COUNT(*) FROM Grades) AS NVARCHAR(10));
PRINT '';
PRINT '=== REFERENTIAL INTEGRITY CHECK ===';
PRINT 'All foreign key relationships have been validated.';
PRINT 'Sample data demonstrates realistic university scenarios:';
PRINT '- Students enrolled in multiple courses';
PRINT '- Cross-departmental course enrollment';
PRINT '- Mix of completed and in-progress courses';
PRINT '- Realistic grade distributions (0.0-4.0 GPA scale)';
PRINT '';
PRINT '=== READY FOR LEARNING EXERCISES ===';
PRINT 'Database is populated and ready for fundamental SQL queries:';
PRINT '- SELECT statements with WHERE clauses';
PRINT '- JOIN operations between related tables';
PRINT '- GROUP BY and aggregate functions';
PRINT '- Subqueries and basic data analysis';
PRINT '';

GO
