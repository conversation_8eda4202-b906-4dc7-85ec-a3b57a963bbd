-- =============================================
-- BASIC VIEWS - FUNDAMENTAL LEVEL
-- =============================================
-- Simple views for learning basic SQL concepts
-- Views are like "saved queries" that you can reuse

USE UNIVERSITY;

-- =============================================
-- SIMPLE VIEWS FOR BEGINNERS
-- =============================================

-- View 1: Student Information with Department
-- This combines student data with department names
CREATE VIEW vw_StudentInfo AS
SELECT
    S.StudentID,
    S.FirstName + ' ' + S.LastName AS FullName,
    S.Email,
    S.EnrollmentYear,
    D.Name AS Department,
    D.Building
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID;

-- View 2: Course Catalog
-- This shows all courses with lecturer and department info
CREATE VIEW vw_CourseCatalog AS
SELECT
    C.Code,
    C.Name AS CourseName,
    C.Credits,
    D.Name AS Department,
    L.FirstName + ' ' + L.LastName AS Lecturer
FROM Courses C
JOIN Departments D ON C.DepartmentID = D.DepartmentID
JOIN Lecturers L ON C.LecturerID = L.LecturerID;

-- View 3: Student Grades Report
-- This shows students with their grades
CREATE VIEW vw_StudentGrades AS
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    G.Grade,
    CASE
        WHEN G.Grade >= 3.7 THEN 'A'
        WHEN G.Grade >= 3.3 THEN 'B+'
        WHEN G.Grade >= 3.0 THEN 'B'
        WHEN G.Grade >= 2.7 THEN 'C+'
        WHEN G.Grade >= 2.0 THEN 'C'
        ELSE 'F'
    END AS LetterGrade
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
JOIN Courses C ON G.CourseID = C.CourseID;

-- =============================================
-- HOW TO USE VIEWS
-- =============================================

-- Now you can query views just like tables:
-- SELECT * FROM vw_StudentInfo;
-- SELECT * FROM vw_CourseCatalog;
-- SELECT * FROM vw_StudentGrades WHERE LetterGrade = 'A';

-- =============================================
-- BASIC INSERT/UPDATE EXAMPLES
-- =============================================
-- These are simple examples of how to add/modify data

-- Example: Add a new student
-- INSERT INTO Students (FirstName, LastName, Email, EnrollmentYear, DepartmentID)
-- VALUES ('New', 'Student', '<EMAIL>', 2024, 1);

-- Example: Add a new enrollment
-- INSERT INTO Enrollments (StudentID, CourseID)
-- VALUES (1, 4);  -- Enroll student 1 in course 4

-- Example: Add a grade
-- INSERT INTO Grades (StudentID, CourseID, Grade)
-- VALUES (1, 4, 3.5);  -- Give student 1 a B+ in course 4

-- Example: Update a student's email
-- UPDATE Students
-- SET Email = '<EMAIL>'
-- WHERE StudentID = 1;

-- =============================================
-- PRACTICE EXERCISES
-- =============================================
-- Try these queries to practice:

-- 1. Show all students from Computer Science department
-- SELECT * FROM vw_StudentInfo WHERE Department = 'Computer Science';

-- 2. Show all courses worth 4 credits
-- SELECT * FROM vw_CourseCatalog WHERE Credits = 4;

-- 3. Show all A grades
-- SELECT * FROM vw_StudentGrades WHERE LetterGrade = 'A';

-- 4. Count students by department
-- SELECT Department, COUNT(*) as StudentCount
-- FROM vw_StudentInfo
-- GROUP BY Department;

-- 5. Show average grade by course
-- SELECT Code, CourseName, AVG(Grade) as AverageGrade
-- FROM vw_StudentGrades
-- GROUP BY Code, CourseName;

-- =============================================
-- ADDITIONAL FILES IN THIS PROJECT
-- =============================================
/*
This project now includes several additional files for comprehensive learning:

 basic_procedures.sql
   - Simple stored procedures with T-SQL concepts
   - Variables, IF...ELSE, CASE statements
   - Error handling and business logic

 advanced_queries.sql
   - Subqueries and CTEs
   - Window functions (ROW_NUMBER, RANK, etc.)
   - Advanced JOIN techniques
   - Complex aggregations

 security_and_backup.sql
   - User management and permissions
   - Role-based security
   - Backup and restore procedures
   - Data encryption basics

 transactions_and_concurrency.sql
   - Transaction concepts and ACID properties
   - Isolation levels and locking
   - Deadlock prevention
   - Concurrency control

These files build upon the fundamental concepts while introducing
more advanced topics in a beginner-friendly way.
*/
