-- Views and Stored Procedures for University Course Management System
USE UNIVERSITY;

-- =============================================
-- VIEWS
-- =============================================

-- View: Student Course Summary
CREATE VIEW vw_StudentCourseSummary AS
SELECT 
    S.StudentID,
    S.FirstName + ' ' + S.LastName AS StudentName,
    S.EnrollmentYear,
    D.Name AS Department,
    C.CourseID,
    C.Code AS CourseCode,
    C.Name AS CourseName,
    C.Credits,
    E.EnrollmentDate,
    G.Grade,
    G.GradeDate,
    CASE 
        WHEN G.Grade IS NULL THEN 'Not Graded'
        WHEN G.Grade >= 4.5 THEN 'Excellent'
        WHEN G.Grade >= 4.0 THEN 'Good'
        WHEN G.Grade >= 3.0 THEN 'Satisfactory'
        ELSE 'Unsatisfactory'
    END AS GradeCategory
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
LEFT JOIN Grades G ON S.StudentID = G.StudentID AND C.CourseID = G.CourseID;

-- View: Course Details with Schedule
CREATE VIEW vw_CourseDetails AS
SELECT 
    C.CourseID,
    C.Code,
    C.Name AS CourseName,
    C.Credits,
    D.Name AS Department,
    L.Title + ' ' + L.FirstName + ' ' + L.LastName AS Lecturer,
    CS.Room,
    CS.DayOfWeek,
    CS.StartTime,
    CS.EndTime,
    COUNT(E.StudentID) AS EnrolledStudents,
    AVG(G.Grade) AS AverageGrade
FROM Courses C
JOIN Departments D ON C.DepartmentID = D.DepartmentID
JOIN Lecturers L ON C.LecturerID = L.LecturerID
LEFT JOIN CourseSchedule CS ON C.CourseID = CS.CourseID
LEFT JOIN Enrollments E ON C.CourseID = E.CourseID
LEFT JOIN Grades G ON C.CourseID = G.CourseID
GROUP BY C.CourseID, C.Code, C.Name, C.Credits, D.Name, 
         L.Title, L.FirstName, L.LastName, CS.Room, CS.DayOfWeek, CS.StartTime, CS.EndTime;

-- View: Department Statistics
CREATE VIEW vw_DepartmentStats AS
SELECT 
    D.DepartmentID,
    D.Name AS DepartmentName,
    D.Building,
    COUNT(DISTINCT S.StudentID) AS TotalStudents,
    COUNT(DISTINCT L.LecturerID) AS TotalLecturers,
    COUNT(DISTINCT C.CourseID) AS TotalCourses,
    AVG(CAST(C.Credits AS FLOAT)) AS AverageCredits,
    SUM(C.Credits) AS TotalCredits
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
LEFT JOIN Lecturers L ON D.DepartmentID = L.DepartmentID
LEFT JOIN Courses C ON D.DepartmentID = C.DepartmentID
GROUP BY D.DepartmentID, D.Name, D.Building;

-- =============================================
-- STORED PROCEDURES
-- =============================================

-- Procedure: Enroll Student in Course
CREATE PROCEDURE sp_EnrollStudent
    @StudentID INT,
    @CourseID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Check if student exists
    IF NOT EXISTS (SELECT 1 FROM Students WHERE StudentID = @StudentID)
    BEGIN
        RAISERROR('Student with ID %d does not exist.', 16, 1, @StudentID);
        RETURN;
    END
    
    -- Check if course exists
    IF NOT EXISTS (SELECT 1 FROM Courses WHERE CourseID = @CourseID)
    BEGIN
        RAISERROR('Course with ID %d does not exist.', 16, 1, @CourseID);
        RETURN;
    END
    
    -- Check if already enrolled
    IF EXISTS (SELECT 1 FROM Enrollments WHERE StudentID = @StudentID AND CourseID = @CourseID)
    BEGIN
        RAISERROR('Student is already enrolled in this course.', 16, 1);
        RETURN;
    END
    
    -- Enroll student
    INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate)
    VALUES (@StudentID, @CourseID, GETDATE());
    
    PRINT 'Student successfully enrolled in course.';
END;

-- Procedure: Add Grade for Student
CREATE PROCEDURE sp_AddGrade
    @StudentID INT,
    @CourseID INT,
    @Grade DECIMAL(3,1)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Validate grade range
    IF @Grade < 2.0 OR @Grade > 5.0
    BEGIN
        RAISERROR('Grade must be between 2.0 and 5.0.', 16, 1);
        RETURN;
    END
    
    -- Check if student is enrolled in course
    IF NOT EXISTS (SELECT 1 FROM Enrollments WHERE StudentID = @StudentID AND CourseID = @CourseID)
    BEGIN
        RAISERROR('Student is not enrolled in this course.', 16, 1);
        RETURN;
    END
    
    -- Check if grade already exists
    IF EXISTS (SELECT 1 FROM Grades WHERE StudentID = @StudentID AND CourseID = @CourseID)
    BEGIN
        -- Update existing grade
        UPDATE Grades 
        SET Grade = @Grade, GradeDate = GETDATE()
        WHERE StudentID = @StudentID AND CourseID = @CourseID;
        
        PRINT 'Grade updated successfully.';
    END
    ELSE
    BEGIN
        -- Insert new grade
        INSERT INTO Grades (StudentID, CourseID, Grade, GradeDate)
        VALUES (@StudentID, @CourseID, @Grade, GETDATE());
        
        PRINT 'Grade added successfully.';
    END
END;

-- Procedure: Get Student Transcript
CREATE PROCEDURE sp_GetStudentTranscript
    @StudentID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Check if student exists
    IF NOT EXISTS (SELECT 1 FROM Students WHERE StudentID = @StudentID)
    BEGIN
        RAISERROR('Student with ID %d does not exist.', 16, 1, @StudentID);
        RETURN;
    END
    
    -- Student information
    SELECT 
        S.FirstName + ' ' + S.LastName AS StudentName,
        S.EnrollmentYear,
        D.Name AS Department
    FROM Students S
    JOIN Departments D ON S.DepartmentID = D.DepartmentID
    WHERE S.StudentID = @StudentID;
    
    -- Course grades
    SELECT 
        C.Code,
        C.Name AS CourseName,
        C.Credits,
        G.Grade,
        G.GradeDate,
        CASE 
            WHEN G.Grade >= 4.5 THEN 'Excellent'
            WHEN G.Grade >= 4.0 THEN 'Good'
            WHEN G.Grade >= 3.0 THEN 'Satisfactory'
            ELSE 'Unsatisfactory'
        END AS GradeCategory
    FROM Enrollments E
    JOIN Courses C ON E.CourseID = C.CourseID
    LEFT JOIN Grades G ON E.StudentID = G.StudentID AND E.CourseID = G.CourseID
    WHERE E.StudentID = @StudentID
    ORDER BY G.GradeDate DESC, C.Code;
    
    -- Summary statistics
    SELECT 
        COUNT(E.CourseID) AS TotalCoursesEnrolled,
        COUNT(G.Grade) AS TotalCoursesGraded,
        SUM(CASE WHEN G.Grade IS NOT NULL THEN C.Credits ELSE 0 END) AS TotalCreditsEarned,
        AVG(G.Grade) AS GPA
    FROM Enrollments E
    JOIN Courses C ON E.CourseID = C.CourseID
    LEFT JOIN Grades G ON E.StudentID = G.StudentID AND E.CourseID = G.CourseID
    WHERE E.StudentID = @StudentID;
END;
