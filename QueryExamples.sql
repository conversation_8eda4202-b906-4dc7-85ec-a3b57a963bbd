USE UniversityCourseManagement;

-- =============================================
-- 1. BASIC SELECT OPERATIONS
-- =============================================

-- 1.1 View all records from a table
SELECT * FROM Departments;

-- 1.2 Select specific columns
SELECT FirstName, LastName, Email FROM Students;

-- 1.3 Select with column aliases
SELECT 
    Code as CourseCode,
    Name as CourseName,
    Credits as CreditHours
FROM Courses;

-- 1.4 Using expressions in SELECT
SELECT 
    FirstName + ' ' + LastName as FullName,
    'Class of ' + CAST(EnrollmentYear AS NVARCHAR(4)) as ClassYear
FROM Students;

-- =============================================
-- 2. FILTERING WITH WHERE CLAUSE
-- =============================================

-- 2.1 Exact match filtering
SELECT FirstName, LastName, EnrollmentYear 
FROM Students 
WHERE EnrollmentYear = 2023;

-- 2.2 Numeric range filtering
SELECT Code, Name, Credits 
FROM Courses 
WHERE Credits BETWEEN 3 AND 4;

-- 2.3 Text pattern matching
SELECT FirstName, LastName 
FROM Students 
WHERE FirstName LIKE 'J%';

-- 2.4 Multiple conditions
SELECT FirstName, LastName, EnrollmentYear
FROM Students
WHERE EnrollmentYear IN (2022, 2023)
  AND FirstName LIKE 'A%';

-- =============================================
-- 3. SORTING AND ORDERING
-- =============================================

-- 3.1 Simple sorting
SELECT FirstName, LastName, EnrollmentYear
FROM Students
ORDER BY LastName;

-- 3.2 Multiple column sorting
SELECT FirstName, LastName, EnrollmentYear
FROM Students
ORDER BY EnrollmentYear DESC, LastName ASC;

-- 3.3 Sorting with calculated fields
SELECT Code, Name, Credits
FROM Courses
ORDER BY Credits DESC, Name;

-- =============================================
-- 4. INNER JOIN OPERATIONS
-- =============================================

-- 4.1 Simple two-table join
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    d.Name as Department,
    d.Building
FROM Students s
INNER JOIN Departments d ON s.DepartmentID = d.DepartmentID;

-- 4.2 Three-table join
SELECT 
    c.Code,
    c.Name as CourseName,
    l.FirstName + ' ' + l.LastName as Lecturer,
    d.Name as Department
FROM Courses c
INNER JOIN Lecturers l ON c.LecturerID = l.LecturerID
INNER JOIN Departments d ON c.DepartmentID = d.DepartmentID;

-- 4.3 Complex join with enrollment data
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    c.Code,
    c.Name as CourseName,
    c.Credits,
    e.EnrollmentDate
FROM Students s
INNER JOIN Enrollments e ON s.StudentID = e.StudentID
INNER JOIN Courses c ON e.CourseID = c.CourseID
ORDER BY s.LastName, c.Code;

-- 4.4 Join with grades information
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    c.Code,
    c.Name as CourseName,
    g.Grade,
    CASE 
        WHEN g.Grade >= 3.7 THEN 'A'
        WHEN g.Grade >= 3.3 THEN 'B+'
        WHEN g.Grade >= 3.0 THEN 'B'
        WHEN g.Grade >= 2.7 THEN 'C+'
        WHEN g.Grade >= 2.0 THEN 'C'
        ELSE 'F'
    END as LetterGrade
FROM Students s
INNER JOIN Grades g ON s.StudentID = g.StudentID
INNER JOIN Courses c ON g.CourseID = c.CourseID
ORDER BY s.LastName, g.Grade DESC;

-- =============================================
-- 5. LEFT JOIN OPERATIONS
-- =============================================

-- 5.1 All students and their enrollments (including students with no enrollments)
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    c.Code,
    c.Name as CourseName
FROM Students s
LEFT JOIN Enrollments e ON s.StudentID = e.StudentID
LEFT JOIN Courses c ON e.CourseID = c.CourseID
ORDER BY s.LastName;

-- 5.2 Enrollments showing which courses need grades
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    c.Code,
    c.Name as CourseName,
    CASE 
        WHEN g.Grade IS NOT NULL THEN CAST(g.Grade AS NVARCHAR(10))
        ELSE 'In Progress'
    END as Status
FROM Students s
INNER JOIN Enrollments e ON s.StudentID = e.StudentID
INNER JOIN Courses c ON e.CourseID = c.CourseID
LEFT JOIN Grades g ON s.StudentID = g.StudentID AND c.CourseID = g.CourseID
ORDER BY s.LastName, c.Code;

-- =============================================
-- 6. AGGREGATE FUNCTIONS
-- =============================================

-- 6.1 Count total records
SELECT COUNT(*) AS TotalStudents FROM Students;
SELECT COUNT(*) AS TotalCourses FROM Courses;

-- 6.2 Count with GROUP BY
SELECT 
    d.Name as Department,
    COUNT(s.StudentID) as StudentCount
FROM Departments d
LEFT JOIN Students s ON d.DepartmentID = s.DepartmentID
GROUP BY d.Name
ORDER BY StudentCount DESC;

-- 6.3 Average calculations
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    COUNT(g.Grade) as CoursesCompleted,
    AVG(g.Grade) as GPA
FROM Students s
LEFT JOIN Grades g ON s.StudentID = g.StudentID
GROUP BY s.StudentID, s.FirstName, s.LastName
ORDER BY GPA DESC;

-- 6.4 Multiple aggregates
SELECT 
    d.Name as Department,
    COUNT(DISTINCT s.StudentID) as TotalStudents,
    COUNT(DISTINCT c.CourseID) as TotalCourses,
    COUNT(DISTINCT l.LecturerID) as TotalLecturers
FROM Departments d
LEFT JOIN Students s ON d.DepartmentID = s.DepartmentID
LEFT JOIN Courses c ON d.DepartmentID = c.DepartmentID
LEFT JOIN Lecturers l ON d.DepartmentID = l.DepartmentID
GROUP BY d.Name
ORDER BY TotalStudents DESC;

-- =============================================
-- 7. HAVING CLAUSE
-- =============================================

-- 7.1 Filter grouped results
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    AVG(g.Grade) as GPA
FROM Students s
INNER JOIN Grades g ON s.StudentID = g.StudentID
GROUP BY s.StudentID, s.FirstName, s.LastName
HAVING AVG(g.Grade) > 3.5
ORDER BY GPA DESC;

-- 7.2 Courses with multiple enrollments
SELECT 
    c.Code,
    c.Name as CourseName,
    COUNT(e.StudentID) as EnrolledStudents
FROM Courses c
INNER JOIN Enrollments e ON c.CourseID = e.CourseID
GROUP BY c.CourseID, c.Code, c.Name
HAVING COUNT(e.StudentID) > 2
ORDER BY EnrolledStudents DESC;

-- =============================================
-- 8. ADVANCED QUERIES
-- =============================================

-- 8.1 Students taking courses outside their major
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    sd.Name as StudentDepartment,
    c.Code,
    c.Name as CourseName,
    cd.Name as CourseDepartment
FROM Students s
INNER JOIN Departments sd ON s.DepartmentID = sd.DepartmentID
INNER JOIN Enrollments e ON s.StudentID = e.StudentID
INNER JOIN Courses c ON e.CourseID = c.CourseID
INNER JOIN Departments cd ON c.DepartmentID = cd.DepartmentID
WHERE s.DepartmentID != c.DepartmentID
ORDER BY s.LastName, c.Code;

-- 8.2 Grade distribution analysis
SELECT 
    CASE 
        WHEN Grade >= 3.7 THEN 'A (3.7-4.0)'
        WHEN Grade >= 3.3 THEN 'B+ (3.3-3.6)'
        WHEN Grade >= 3.0 THEN 'B (3.0-3.2)'
        WHEN Grade >= 2.7 THEN 'C+ (2.7-2.9)'
        WHEN Grade >= 2.0 THEN 'C (2.0-2.6)'
        ELSE 'F (0.0-1.9)'
    END as LetterGrade,
    COUNT(*) as NumberOfGrades,
    CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Grades) AS DECIMAL(5,2)) as Percentage
FROM Grades
GROUP BY 
    CASE 
        WHEN Grade >= 3.7 THEN 'A (3.7-4.0)'
        WHEN Grade >= 3.3 THEN 'B+ (3.3-3.6)'
        WHEN Grade >= 3.0 THEN 'B (3.0-3.2)'
        WHEN Grade >= 2.7 THEN 'C+ (2.7-2.9)'
        WHEN Grade >= 2.0 THEN 'C (2.0-2.6)'
        ELSE 'F (0.0-1.9)'
    END
ORDER BY MIN(Grade) DESC;

-- 8.3 Faculty workload analysis
SELECT 
    l.FirstName + ' ' + l.LastName as LecturerName,
    d.Name as Department,
    COUNT(c.CourseID) as CoursesTeaching,
    SUM(c.Credits) as TotalCreditHours
FROM Lecturers l
INNER JOIN Departments d ON l.DepartmentID = d.DepartmentID
LEFT JOIN Courses c ON l.LecturerID = c.LecturerID
GROUP BY l.LecturerID, l.FirstName, l.LastName, d.Name
ORDER BY TotalCreditHours DESC;

-- =============================================
-- 9. USEFUL BUSINESS REPORTS
-- =============================================

-- 9.1 Complete student roster
SELECT 
    s.FirstName + ' ' + s.LastName as StudentName,
    s.Email,
    s.EnrollmentYear,
    d.Name as Department,
    d.Building
FROM Students s
INNER JOIN Departments d ON s.DepartmentID = d.DepartmentID
ORDER BY d.Name, s.LastName;

-- 9.2 Course catalog
SELECT 
    c.Code,
    c.Name as CourseName,
    c.Credits,
    d.Name as Department,
    l.FirstName + ' ' + l.LastName as Lecturer
FROM Courses c
INNER JOIN Departments d ON c.DepartmentID = d.DepartmentID
INNER JOIN Lecturers l ON c.LecturerID = l.LecturerID
ORDER BY d.Name, c.Code;

-- 9.3 Academic performance summary
SELECT 
    d.Name as Department,
    COUNT(DISTINCT s.StudentID) as TotalStudents,
    COUNT(g.Grade) as CompletedCourses,
    AVG(g.Grade) as DepartmentGPA
FROM Departments d
LEFT JOIN Students s ON d.DepartmentID = s.DepartmentID
LEFT JOIN Grades g ON s.StudentID = g.StudentID
GROUP BY d.Name
ORDER BY DepartmentGPA DESC;

-- =============================================
-- END OF QUERY EXAMPLES
-- =============================================
-- Practice these queries to master fundamental SQL concepts!
-- Start with basic SELECT and progress through JOINs and aggregations.
