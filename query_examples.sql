-- =============================================
-- FUNDAMENTAL SQL QUERY EXAMPLES
-- =============================================
-- Basic queries for learning core SQL concepts
-- Focus: SELECT, WHERE, JOIN, GROUP BY, ORDER BY, basic functions

USE UNIVERSITY;

-- =============================================
-- 1. BASIC SELECT QUERIES
-- =============================================

-- Show all students
SELECT * FROM Students;

-- Show specific columns
SELECT FirstName, LastName, Email FROM Students;

-- Show students with a condition
SELECT FirstName, LastName, EnrollmentYear
FROM Students
WHERE EnrollmentYear = 2023;

-- Show students ordered by last name
SELECT FirstName, LastName, EnrollmentYear
FROM Students
ORDER BY LastName;

-- =============================================
-- 2. BASIC JOIN QUERIES (Most Important!)
-- =============================================

-- Students with their department names (INNER JOIN)
SELECT
    S.FirstName,
    S.LastName,
    D.Name AS Department
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID;

-- Courses with their lecturers
SELECT
    C.Code,
    C.Name AS CourseName,
    L.FirstName AS LecturerFirstName,
    L.LastName AS LecturerLastName
FROM Courses C
JOIN Lecturers L ON C.LecturerID = L.LecturerID;

-- Students and their enrolled courses
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
ORDER BY S.LastName;

-- =============================================
-- 3. WORKING WITH GRADES
-- =============================================

-- Students with their grades
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    G.Grade
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
JOIN Courses C ON G.CourseID = C.CourseID
ORDER BY G.Grade DESC;

-- Students without grades (LEFT JOIN)
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    G.Grade
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
LEFT JOIN Grades G ON S.StudentID = G.StudentID AND C.CourseID = G.CourseID
WHERE G.Grade IS NULL;

-- =============================================
-- 4. BASIC AGGREGATE FUNCTIONS
-- =============================================

-- Count total students
SELECT COUNT(*) AS TotalStudents FROM Students;

-- Count students by department
SELECT
    D.Name AS Department,
    COUNT(S.StudentID) AS NumberOfStudents
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
GROUP BY D.DepartmentID, D.Name;

-- Average grade per course
SELECT
    C.Code,
    C.Name AS CourseName,
    COUNT(G.Grade) AS NumberOfGrades,
    AVG(G.Grade) AS AverageGrade
FROM Courses C
LEFT JOIN Grades G ON C.CourseID = G.CourseID
GROUP BY C.CourseID, C.Code, C.Name
ORDER BY AverageGrade DESC;

-- Student's average grade (GPA)
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    COUNT(G.Grade) AS CoursesCompleted,
    AVG(G.Grade) AS GPA
FROM Students S
LEFT JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName
ORDER BY GPA DESC;

-- =============================================
-- 5. FILTERING WITH HAVING
-- =============================================

-- Students with GPA above 3.5
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    AVG(G.Grade) AS GPA
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName
HAVING AVG(G.Grade) > 3.5
ORDER BY GPA DESC;

-- Courses with more than 1 student enrolled
SELECT
    C.Code,
    C.Name AS CourseName,
    COUNT(E.StudentID) AS EnrolledStudents
FROM Courses C
JOIN Enrollments E ON C.CourseID = E.CourseID
GROUP BY C.CourseID, C.Code, C.Name
HAVING COUNT(E.StudentID) > 1;

-- =============================================
-- 6. USEFUL REPORTS
-- =============================================

-- Complete student roster with departments
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    S.Email,
    S.EnrollmentYear,
    D.Name AS Department,
    D.Building
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
ORDER BY D.Name, S.LastName;

-- Course catalog
SELECT
    C.Code,
    C.Name AS CourseName,
    C.Credits,
    D.Name AS Department,
    L.FirstName + ' ' + L.LastName AS Lecturer
FROM Courses C
JOIN Departments D ON C.DepartmentID = D.DepartmentID
JOIN Lecturers L ON C.LecturerID = L.LecturerID
ORDER BY D.Name, C.Code;

-- Department summary
SELECT
    D.Name AS Department,
    D.Building,
    COUNT(DISTINCT S.StudentID) AS TotalStudents,
    COUNT(DISTINCT C.CourseID) AS TotalCourses
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
LEFT JOIN Courses C ON D.DepartmentID = C.DepartmentID
GROUP BY D.DepartmentID, D.Name, D.Building
ORDER BY TotalStudents DESC;
