-- Query Examples for University Course Management System
USE UNIVERSITY;

-- 1. Basic Queries

-- List all students with their departments
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    S.EnrollmentYear,
    D.Name AS Department,
    D.Building
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
ORDER BY S.LastName, S.FirstName;

-- List all courses with lecturer information
SELECT 
    C.Code,
    C.Name AS CourseName,
    C.Credits,
    L.Title + ' ' + L.FirstName + ' ' + L.LastName AS Lecturer,
    D.Name AS Department
FROM Courses C
JOIN Lecturers L ON C.LecturerID = L.LecturerID
JOIN Departments D ON C.DepartmentID = D.DepartmentID
ORDER BY D.Name, C.Code;

-- 2. Relationship Queries

-- Students and their enrolled courses
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    C.Credits,
    E.EnrollmentDate
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
ORDER BY S.LastName, C.Code;

-- Students with their grades
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    G.Grade,
    G.GradeDate
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
JOIN Courses C ON G.CourseID = C.CourseID
ORDER BY S.LastName, G.Grade DESC;

-- 3. Aggregation Queries

-- Average grade per course
SELECT 
    C.Code,
    C.Name AS CourseName,
    COUNT(G.Grade) AS NumberOfGrades,
    AVG(G.Grade) AS AverageGrade,
    MIN(G.Grade) AS MinGrade,
    MAX(G.Grade) AS MaxGrade
FROM Courses C
LEFT JOIN Grades G ON C.CourseID = G.CourseID
GROUP BY C.CourseID, C.Code, C.Name
ORDER BY AverageGrade DESC;

-- Student performance summary
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    D.Name AS Department,
    COUNT(E.CourseID) AS CoursesEnrolled,
    COUNT(G.Grade) AS CoursesGraded,
    AVG(G.Grade) AS AverageGrade
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
LEFT JOIN Enrollments E ON S.StudentID = E.StudentID
LEFT JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName, D.Name
ORDER BY AverageGrade DESC;

-- Department statistics
SELECT 
    D.Name AS Department,
    COUNT(DISTINCT S.StudentID) AS NumberOfStudents,
    COUNT(DISTINCT L.LecturerID) AS NumberOfLecturers,
    COUNT(DISTINCT C.CourseID) AS NumberOfCourses,
    AVG(C.Credits) AS AverageCredits
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
LEFT JOIN Lecturers L ON D.DepartmentID = L.DepartmentID
LEFT JOIN Courses C ON D.DepartmentID = C.DepartmentID
GROUP BY D.DepartmentID, D.Name
ORDER BY NumberOfStudents DESC;

-- 4. Schedule Queries

-- Complete course schedule
SELECT 
    C.Code,
    C.Name AS CourseName,
    L.Title + ' ' + L.FirstName + ' ' + L.LastName AS Lecturer,
    CS.Room,
    CS.DayOfWeek,
    CS.StartTime,
    CS.EndTime,
    DATEDIFF(MINUTE, CS.StartTime, CS.EndTime) AS DurationMinutes
FROM CourseSchedule CS
JOIN Courses C ON CS.CourseID = C.CourseID
JOIN Lecturers L ON C.LecturerID = L.LecturerID
ORDER BY CS.DayOfWeek, CS.StartTime;

-- Room utilization
SELECT 
    CS.Room,
    COUNT(*) AS NumberOfClasses,
    COUNT(DISTINCT CS.CourseID) AS NumberOfCourses,
    SUM(DATEDIFF(MINUTE, CS.StartTime, CS.EndTime)) AS TotalMinutesPerWeek
FROM CourseSchedule CS
GROUP BY CS.Room
ORDER BY TotalMinutesPerWeek DESC;

-- 5. Advanced Queries

-- Students without grades (enrolled but not yet graded)
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    E.EnrollmentDate
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
LEFT JOIN Grades G ON S.StudentID = G.StudentID AND C.CourseID = G.CourseID
WHERE G.Grade IS NULL
ORDER BY E.EnrollmentDate;

-- Top performing students (GPA >= 4.0)
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    D.Name AS Department,
    AVG(G.Grade) AS GPA,
    COUNT(G.Grade) AS CoursesCompleted
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName, D.Name
HAVING AVG(G.Grade) >= 4.0
ORDER BY GPA DESC;

-- Courses with no enrollments
SELECT 
    C.Code,
    C.Name AS CourseName,
    L.FirstName + ' ' + L.LastName AS Lecturer,
    D.Name AS Department
FROM Courses C
JOIN Lecturers L ON C.LecturerID = L.LecturerID
JOIN Departments D ON C.DepartmentID = D.DepartmentID
LEFT JOIN Enrollments E ON C.CourseID = E.CourseID
WHERE E.CourseID IS NULL
ORDER BY D.Name, C.Code;
