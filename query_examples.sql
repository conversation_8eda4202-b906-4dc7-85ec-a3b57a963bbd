-- =============================================
-- FUNDAMENTAL SQL QUERY EXAMPLES
-- =============================================
-- Basic queries for learning core SQL concepts
-- Focus: SELECT, WHERE, JOIN, GROUP BY, ORDER BY, basic functions

USE UNIVERSITY;

-- =============================================
-- 1. BASIC SELECT QUERIES
-- =============================================

-- Show all students
SELECT * FROM Students;

-- Show specific columns
SELECT FirstName, LastName, Email FROM Students;

-- Show students with a condition
SELECT FirstName, LastName, EnrollmentYear
FROM Students
WHERE EnrollmentYear = 2023;

-- Show students ordered by last name
SELECT FirstName, LastName, EnrollmentYear
FROM Students
ORDER BY LastName;

-- =============================================
-- 2. BASIC JOIN QUERIES (Most Important!)
-- =============================================

-- Students with their department names (INNER JOIN)
SELECT
    S.FirstName,
    S.LastName,
    D.Name AS Department
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID;

-- Courses with their lecturers
SELECT
    C.Code,
    C.Name AS CourseName,
    L.FirstName AS LecturerFirstName,
    L.LastName AS LecturerLastName
FROM Courses C
JOIN Lecturers L ON C.LecturerID = L.LecturerID;

-- Students and their enrolled courses
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
ORDER BY S.LastName;

-- =============================================
-- 3. WORKING WITH GRADES
-- =============================================

-- Students with their grades
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    G.Grade
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
JOIN Courses C ON G.CourseID = C.CourseID
ORDER BY G.Grade DESC;

-- Students without grades (LEFT JOIN)
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    C.Name AS CourseName,
    G.Grade
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
LEFT JOIN Grades G ON S.StudentID = G.StudentID AND C.CourseID = G.CourseID
WHERE G.Grade IS NULL;

-- =============================================
-- 4. BASIC AGGREGATE FUNCTIONS
-- =============================================

-- Count total students
SELECT COUNT(*) AS TotalStudents FROM Students;

-- Count students by department
SELECT
    D.Name AS Department,
    COUNT(S.StudentID) AS NumberOfStudents
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
GROUP BY D.DepartmentID, D.Name;

-- Average grade per course
SELECT
    C.Code,
    C.Name AS CourseName,
    COUNT(G.Grade) AS NumberOfGrades,
    AVG(G.Grade) AS AverageGrade
FROM Courses C
LEFT JOIN Grades G ON C.CourseID = G.CourseID
GROUP BY C.CourseID, C.Code, C.Name
ORDER BY AverageGrade DESC;

-- Student's average grade (GPA)
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    COUNT(G.Grade) AS CoursesCompleted,
    AVG(G.Grade) AS GPA
FROM Students S
LEFT JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName
ORDER BY GPA DESC;

-- =============================================
-- 5. FILTERING WITH HAVING
-- =============================================

-- Students with GPA above 3.5
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    AVG(G.Grade) AS GPA
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName
HAVING AVG(G.Grade) > 3.5
ORDER BY GPA DESC;

-- Courses with more than 1 student enrolled
SELECT
    C.Code,
    C.Name AS CourseName,
    COUNT(E.StudentID) AS EnrolledStudents
FROM Courses C
JOIN Enrollments E ON C.CourseID = E.CourseID
GROUP BY C.CourseID, C.Code, C.Name
HAVING COUNT(E.StudentID) > 1;

-- =============================================
-- 6. USEFUL REPORTS
-- =============================================

-- Complete student roster with departments
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    S.Email,
    S.EnrollmentYear,
    D.Name AS Department,
    D.Building
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
ORDER BY D.Name, S.LastName;

-- Course catalog
SELECT
    C.Code,
    C.Name AS CourseName,
    C.Credits,
    D.Name AS Department,
    L.FirstName + ' ' + L.LastName AS Lecturer
FROM Courses C
JOIN Departments D ON C.DepartmentID = D.DepartmentID
JOIN Lecturers L ON C.LecturerID = L.LecturerID
ORDER BY D.Name, C.Code;

-- Department summary
SELECT
    D.Name AS Department,
    D.Building,
    COUNT(DISTINCT S.StudentID) AS TotalStudents,
    COUNT(DISTINCT C.CourseID) AS TotalCourses
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
LEFT JOIN Courses C ON D.DepartmentID = C.DepartmentID
GROUP BY D.DepartmentID, D.Name, D.Building
ORDER BY TotalStudents DESC;

-- =============================================
-- 7. ADVANCED LEARNING QUERIES
-- =============================================

-- Students taking courses outside their major department
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    SD.Name AS StudentDepartment,
    C.Code,
    C.Name AS CourseName,
    CD.Name AS CourseDepartment
FROM Students S
JOIN Departments SD ON S.DepartmentID = SD.DepartmentID
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
JOIN Departments CD ON C.DepartmentID = CD.DepartmentID
WHERE S.DepartmentID != C.DepartmentID
ORDER BY S.LastName, C.Code;

-- Lecturers and their course load
SELECT
    L.FirstName + ' ' + L.LastName AS LecturerName,
    D.Name AS Department,
    COUNT(C.CourseID) AS CoursesTeaching,
    SUM(C.Credits) AS TotalCreditHours
FROM Lecturers L
JOIN Departments D ON L.DepartmentID = D.DepartmentID
LEFT JOIN Courses C ON L.LecturerID = C.LecturerID
GROUP BY L.LecturerID, L.FirstName, L.LastName, D.Name
ORDER BY TotalCreditHours DESC;

-- Grade distribution analysis
SELECT
    CASE
        WHEN Grade >= 3.7 THEN 'A (3.7-4.0)'
        WHEN Grade >= 3.3 THEN 'B+ (3.3-3.6)'
        WHEN Grade >= 3.0 THEN 'B (3.0-3.2)'
        WHEN Grade >= 2.7 THEN 'C+ (2.7-2.9)'
        WHEN Grade >= 2.0 THEN 'C (2.0-2.6)'
        ELSE 'F (0.0-1.9)'
    END AS LetterGrade,
    COUNT(*) AS NumberOfGrades,
    CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Grades) AS DECIMAL(5,2)) AS Percentage
FROM Grades
GROUP BY
    CASE
        WHEN Grade >= 3.7 THEN 'A (3.7-4.0)'
        WHEN Grade >= 3.3 THEN 'B+ (3.3-3.6)'
        WHEN Grade >= 3.0 THEN 'B (3.0-3.2)'
        WHEN Grade >= 2.7 THEN 'C+ (2.7-2.9)'
        WHEN Grade >= 2.0 THEN 'C (2.0-2.6)'
        ELSE 'F (0.0-1.9)'
    END
ORDER BY MIN(Grade) DESC;

-- =============================================
-- 8. PRACTICE EXERCISES FOR STUDENTS
-- =============================================

-- Try writing queries to answer these questions:

-- BEGINNER LEVEL:
-- 1. List all courses offered by the Mathematics department
-- 2. Find students enrolled in 2024
-- 3. Show all lecturers hired after 2018
-- 4. Count how many courses each department offers

-- INTERMEDIATE LEVEL:
-- 5. Find students who are enrolled in more than 3 courses
-- 6. List courses that have no enrolled students
-- 7. Show the highest grade achieved in each course
-- 8. Find lecturers who teach courses in multiple departments

-- ADVANCED LEVEL:
-- 9. Calculate each student's credit hours attempted (enrolled courses)
-- 10. Find students with perfect 4.0 GPA
-- 11. Show enrollment trends by year and department
-- 12. Identify the most popular courses (highest enrollment)

-- =============================================
-- SAMPLE SOLUTIONS (Try the exercises first!)
-- =============================================

-- Solution 1: Mathematics department courses
-- SELECT C.Code, C.Name, C.Credits
-- FROM Courses C
-- JOIN Departments D ON C.DepartmentID = D.DepartmentID
-- WHERE D.Name = 'Mathematics';

-- Solution 5: Students with more than 3 enrollments
-- SELECT S.FirstName + ' ' + S.LastName AS StudentName,
--        COUNT(E.CourseID) AS CoursesEnrolled
-- FROM Students S
-- JOIN Enrollments E ON S.StudentID = E.StudentID
-- GROUP BY S.StudentID, S.FirstName, S.LastName
-- HAVING COUNT(E.CourseID) > 3;

-- =============================================
-- END OF QUERY EXAMPLES
-- =============================================
-- These examples progress from basic to advanced concepts.
-- Practice each section thoroughly before moving to the next.
-- Remember: The best way to learn SQL is by writing queries!
