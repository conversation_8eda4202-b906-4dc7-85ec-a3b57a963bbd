-- Database Extensions for University Course Management System
USE UNIVERSITY;

-- =============================================
-- ADDITIONAL TABLES FOR EXTENSIONS
-- =============================================

-- Prerequisites Table (Course-to-Course Mapping)
CREATE TABLE Prerequisites (
    CourseID INT,
    PrerequisiteCourseID INT,
    PRIMARY KEY(CourseID, PrerequisiteCourseID),
    FOREIGN KEY(CourseID) REFERENCES Courses(CourseID),
    FOREIGN KEY(PrerequisiteCourseID) REFERENCES Courses(CourseID),
    CHECK (CourseID != PrerequisiteCourseID) -- Prevent self-reference
);

-- Semesters Table
CREATE TABLE Semesters (
    SemesterID INT PRIMARY KEY IDENTITY,
    Name NVARCHAR(50) NOT NULL, -- e.g., "Fall 2023", "Spring 2024"
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    AcademicYear INT NOT NULL,
    CHECK (EndDate > StartDate)
);

-- Course Offerings (Courses offered in specific semesters)
CREATE TABLE CourseOfferings (
    OfferingID INT PRIMARY KEY IDENTITY,
    CourseID INT FOREIGN KEY REFERENCES Courses(CourseID),
    SemesterID INT FOREIGN KEY REFERENCES Semesters(SemesterID),
    MaxEnrollment INT DEFAULT 30,
    CurrentEnrollment INT DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Active' CHECK (Status IN ('Active', 'Cancelled', 'Full'))
);

-- Attendance Tracking
CREATE TABLE Attendance (
    AttendanceID INT PRIMARY KEY IDENTITY,
    StudentID INT,
    CourseID INT,
    AttendanceDate DATE NOT NULL,
    Status NVARCHAR(10) CHECK (Status IN ('Present', 'Absent', 'Late', 'Excused')),
    Notes NVARCHAR(200),
    FOREIGN KEY(StudentID) REFERENCES Students(StudentID),
    FOREIGN KEY(CourseID) REFERENCES Courses(CourseID)
);

-- User Roles and Authentication
CREATE TABLE UserRoles (
    RoleID INT PRIMARY KEY IDENTITY,
    RoleName NVARCHAR(50) UNIQUE NOT NULL,
    Description NVARCHAR(200)
);

CREATE TABLE Users (
    UserID INT PRIMARY KEY IDENTITY,
    Username NVARCHAR(50) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    Email NVARCHAR(100) UNIQUE NOT NULL,
    RoleID INT FOREIGN KEY REFERENCES UserRoles(RoleID),
    StudentID INT NULL FOREIGN KEY REFERENCES Students(StudentID),
    LecturerID INT NULL FOREIGN KEY REFERENCES Lecturers(LecturerID),
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastLoginDate DATETIME,
    IsActive BIT DEFAULT 1
);

-- Course Materials
CREATE TABLE CourseMaterials (
    MaterialID INT PRIMARY KEY IDENTITY,
    CourseID INT FOREIGN KEY REFERENCES Courses(CourseID),
    Title NVARCHAR(200) NOT NULL,
    Description NVARCHAR(500),
    MaterialType NVARCHAR(50) CHECK (MaterialType IN ('Lecture Notes', 'Assignment', 'Reading', 'Video', 'Other')),
    FilePath NVARCHAR(500),
    UploadDate DATETIME DEFAULT GETDATE(),
    IsVisible BIT DEFAULT 1
);

-- =============================================
-- SAMPLE DATA FOR EXTENSIONS
-- =============================================

-- Insert User Roles
INSERT INTO UserRoles (RoleName, Description) VALUES
('Admin', 'System administrator with full access'),
('Lecturer', 'Faculty member who can manage their courses'),
('Student', 'Student with access to their own data'),
('Staff', 'Administrative staff with limited access');

-- Insert Semesters
INSERT INTO Semesters (Name, StartDate, EndDate, AcademicYear) VALUES
('Fall 2023', '2023-09-01', '2023-12-15', 2023),
('Spring 2024', '2024-01-15', '2024-05-15', 2024),
('Fall 2024', '2024-09-01', '2024-12-15', 2024),
('Spring 2025', '2025-01-15', '2025-05-15', 2025);

-- Insert Prerequisites
INSERT INTO Prerequisites (CourseID, PrerequisiteCourseID) VALUES
(2, 1), -- CS201 (Database Systems) requires CS101 (Intro to Programming)
(4, 3), -- MATH201 (Linear Algebra) requires MATH101 (Calculus I)
(6, 5); -- PHY301 (Quantum Mechanics) requires PHY101 (General Physics)

-- Insert Course Offerings
INSERT INTO CourseOfferings (CourseID, SemesterID, MaxEnrollment, CurrentEnrollment) VALUES
(1, 1, 30, 2), -- CS101 Fall 2023
(2, 2, 25, 2), -- CS201 Spring 2024
(3, 1, 40, 3), -- MATH101 Fall 2023
(4, 2, 35, 2), -- MATH201 Spring 2024
(5, 1, 30, 2), -- PHY101 Fall 2023
(6, 2, 20, 2), -- PHY301 Spring 2024
(7, 1, 25, 1), -- CHEM101 Fall 2023
(8, 1, 30, 1); -- BIO101 Fall 2023

-- Insert Sample Attendance Records
INSERT INTO Attendance (StudentID, CourseID, AttendanceDate, Status) VALUES
(1, 1, '2023-09-05', 'Present'),
(1, 1, '2023-09-07', 'Present'),
(1, 1, '2023-09-12', 'Late'),
(1, 2, '2024-01-20', 'Present'),
(2, 3, '2023-09-05', 'Present'),
(2, 3, '2023-09-08', 'Absent'),
(3, 5, '2023-09-06', 'Present');

-- Insert Course Materials
INSERT INTO CourseMaterials (CourseID, Title, Description, MaterialType) VALUES
(1, 'Introduction to Programming - Lecture 1', 'Basic programming concepts and syntax', 'Lecture Notes'),
(1, 'Programming Assignment 1', 'Write a simple calculator program', 'Assignment'),
(2, 'Database Design Principles', 'Fundamentals of relational database design', 'Lecture Notes'),
(3, 'Calculus Textbook Chapter 1', 'Limits and Continuity', 'Reading'),
(5, 'Physics Lab Manual', 'Laboratory exercises for general physics', 'Other');

-- =============================================
-- ADVANCED QUERIES AND FUNCTIONS
-- =============================================

-- Function: Calculate Student GPA
CREATE FUNCTION fn_CalculateGPA(@StudentID INT)
RETURNS DECIMAL(3,2)
AS
BEGIN
    DECLARE @GPA DECIMAL(3,2);
    
    SELECT @GPA = AVG(CAST(Grade AS DECIMAL(3,2)))
    FROM Grades
    WHERE StudentID = @StudentID;
    
    RETURN ISNULL(@GPA, 0.00);
END;

-- Function: Check Prerequisites
CREATE FUNCTION fn_CheckPrerequisites(@StudentID INT, @CourseID INT)
RETURNS BIT
AS
BEGIN
    DECLARE @HasPrerequisites BIT = 1;
    
    -- Check if there are any prerequisites not met
    IF EXISTS (
        SELECT 1 
        FROM Prerequisites P
        WHERE P.CourseID = @CourseID
        AND P.PrerequisiteCourseID NOT IN (
            SELECT G.CourseID 
            FROM Grades G 
            WHERE G.StudentID = @StudentID 
            AND G.Grade >= 3.0 -- Minimum passing grade
        )
    )
    BEGIN
        SET @HasPrerequisites = 0;
    END
    
    RETURN @HasPrerequisites;
END;

-- View: Student Academic Standing
CREATE VIEW vw_StudentAcademicStanding AS
SELECT 
    S.StudentID,
    S.FirstName + ' ' + S.LastName AS StudentName,
    D.Name AS Department,
    dbo.fn_CalculateGPA(S.StudentID) AS GPA,
    COUNT(G.Grade) AS CompletedCourses,
    SUM(C.Credits) AS TotalCredits,
    CASE 
        WHEN dbo.fn_CalculateGPA(S.StudentID) >= 4.5 THEN 'Dean''s List'
        WHEN dbo.fn_CalculateGPA(S.StudentID) >= 4.0 THEN 'Honor Roll'
        WHEN dbo.fn_CalculateGPA(S.StudentID) >= 3.0 THEN 'Good Standing'
        WHEN dbo.fn_CalculateGPA(S.StudentID) >= 2.0 THEN 'Academic Warning'
        ELSE 'Academic Probation'
    END AS AcademicStatus
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
LEFT JOIN Grades G ON S.StudentID = G.StudentID
LEFT JOIN Courses C ON G.CourseID = C.CourseID
GROUP BY S.StudentID, S.FirstName, S.LastName, D.Name;

-- Trigger: Update Course Enrollment Count
CREATE TRIGGER tr_UpdateEnrollmentCount
ON Enrollments
AFTER INSERT, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Update for inserted records
    IF EXISTS (SELECT * FROM inserted)
    BEGIN
        UPDATE CO
        SET CurrentEnrollment = (
            SELECT COUNT(*)
            FROM Enrollments E
            WHERE E.CourseID = CO.CourseID
        )
        FROM CourseOfferings CO
        INNER JOIN inserted I ON CO.CourseID = I.CourseID;
    END
    
    -- Update for deleted records
    IF EXISTS (SELECT * FROM deleted)
    BEGIN
        UPDATE CO
        SET CurrentEnrollment = (
            SELECT COUNT(*)
            FROM Enrollments E
            WHERE E.CourseID = CO.CourseID
        )
        FROM CourseOfferings CO
        INNER JOIN deleted D ON CO.CourseID = D.CourseID;
    END
END;
