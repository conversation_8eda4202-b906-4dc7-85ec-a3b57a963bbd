-- =============================================
-- BASIC TRANSACTIONS AND CONCURRENCY
-- =============================================
-- Introduction to transaction concepts, ACID properties, and basic concurrency control
-- Fundamental level - essential concepts for database integrity

USE UNIVERSITY;

-- =============================================
-- 1. BASIC TRANSACTION CONCEPTS
-- =============================================

-- Simple transaction example: Enroll student and add initial grade
BEGIN TRANSACTION;

DECLARE @StudentID INT = 1;
DECLARE @CourseID INT = 4;
DECLARE @Grade DECIMAL(3,1) = 3.5;

-- Check if student and course exist
IF NOT EXISTS (SELECT 1 FROM Students WHERE StudentID = @StudentID)
BEGIN
    PRINT 'Error: Student does not exist';
    ROLLBACK TRANSACTION;
    RETURN;
END

IF NOT EXISTS (SELECT 1 FROM Courses WHERE CourseID = @CourseID)
BEGIN
    PRINT 'Error: Course does not exist';
    ROLLBACK TRANSACTION;
    RETURN;
END

-- Perform the operations
INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate)
VALUES (@StudentID, @CourseID, GETDATE());

INSERT INTO Grades (StudentID, CourseID, Grade)
VALUES (@StudentID, @CourseID, @Grade);

-- If we get here, everything worked
COMMIT TRANSACTION;
PRINT 'Student enrolled and grade added successfully';

-- =============================================
-- 2. TRANSACTION WITH ERROR HANDLING
-- =============================================

-- More robust transaction with TRY...CATCH
BEGIN TRY
    BEGIN TRANSACTION;
    
    DECLARE @NewStudentID INT;
    
    -- Insert a new student
    INSERT INTO Students (FirstName, LastName, Email, EnrollmentYear, DepartmentID)
    VALUES ('Jane', 'Smith', '<EMAIL>', 2024, 1);
    
    SET @NewStudentID = SCOPE_IDENTITY();
    
    -- Enroll them in multiple courses
    INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate)
    VALUES 
        (@NewStudentID, 1, GETDATE()),
        (@NewStudentID, 3, GETDATE());
    
    -- If everything succeeds, commit
    COMMIT TRANSACTION;
    PRINT 'New student added and enrolled successfully. Student ID: ' + CAST(@NewStudentID AS VARCHAR);
    
END TRY
BEGIN CATCH
    -- If anything fails, rollback
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;
    
    PRINT 'Error occurred: ' + ERROR_MESSAGE();
    PRINT 'Transaction rolled back';
END CATCH;

-- =============================================
-- 3. SAVEPOINTS (Partial Rollbacks)
-- =============================================

BEGIN TRANSACTION;

-- Insert a student
INSERT INTO Students (FirstName, LastName, Email, EnrollmentYear, DepartmentID)
VALUES ('Test', 'Student', '<EMAIL>', 2024, 1);

DECLARE @TestStudentID INT = SCOPE_IDENTITY();
SAVE TRANSACTION SavePoint1;

-- Try to enroll in courses
BEGIN TRY
    INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate)
    VALUES (@TestStudentID, 1, GETDATE());
    
    SAVE TRANSACTION SavePoint2;
    
    -- This might fail if course doesn't exist
    INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate)
    VALUES (@TestStudentID, 999, GETDATE()); -- Invalid course ID
    
END TRY
BEGIN CATCH
    -- Rollback only to SavePoint2, keep the student and first enrollment
    ROLLBACK TRANSACTION SavePoint2;
    PRINT 'Second enrollment failed, but keeping first enrollment';
END CATCH;

COMMIT TRANSACTION;
PRINT 'Transaction completed with partial rollback';

-- =============================================
-- 4. ISOLATION LEVELS
-- =============================================

-- Demonstrate different isolation levels

-- READ UNCOMMITTED (lowest isolation, allows dirty reads)
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
BEGIN TRANSACTION;
SELECT * FROM Students WHERE StudentID = 1;
COMMIT;

-- READ COMMITTED (default, prevents dirty reads)
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
BEGIN TRANSACTION;
SELECT * FROM Students WHERE StudentID = 1;
COMMIT;

-- REPEATABLE READ (prevents dirty and non-repeatable reads)
SET TRANSACTION ISOLATION LEVEL REPEATABLE READ;
BEGIN TRANSACTION;
SELECT * FROM Students WHERE StudentID = 1;
-- If we read again, we'll get the same result
SELECT * FROM Students WHERE StudentID = 1;
COMMIT;

-- SERIALIZABLE (highest isolation, prevents all phenomena)
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
BEGIN TRANSACTION;
SELECT * FROM Students WHERE DepartmentID = 1;
-- No new students can be added to department 1 until this commits
COMMIT;

-- Reset to default
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

-- =============================================
-- 5. LOCKING HINTS
-- =============================================

-- NOLOCK hint (same as READ UNCOMMITTED for this query)
SELECT * FROM Students WITH (NOLOCK) WHERE DepartmentID = 1;

-- HOLDLOCK hint (hold shared locks until transaction ends)
BEGIN TRANSACTION;
SELECT * FROM Students WITH (HOLDLOCK) WHERE StudentID = 1;
-- Other transactions can read but not modify this row
COMMIT;

-- UPDLOCK hint (take update lock instead of shared lock)
BEGIN TRANSACTION;
SELECT * FROM Students WITH (UPDLOCK) WHERE StudentID = 1;
-- Prevents other transactions from taking update or exclusive locks
COMMIT;

-- XLOCK hint (take exclusive lock)
BEGIN TRANSACTION;
SELECT * FROM Students WITH (XLOCK) WHERE StudentID = 1;
-- No other transactions can read or write this row
COMMIT;

-- =============================================
-- 6. DEADLOCK DEMONSTRATION
-- =============================================

-- This is an example of how deadlocks can occur
-- DON'T RUN THESE SIMULTANEOUSLY unless you want to see a deadlock!

-- Session 1 would run:
/*
BEGIN TRANSACTION;
UPDATE Students SET Email = '<EMAIL>' WHERE StudentID = 1;
WAITFOR DELAY '00:00:05'; -- Wait 5 seconds
UPDATE Courses SET Name = 'Updated Course' WHERE CourseID = 1;
COMMIT;
*/

-- Session 2 would run simultaneously:
/*
BEGIN TRANSACTION;
UPDATE Courses SET Name = 'Another Update' WHERE CourseID = 1;
WAITFOR DELAY '00:00:05'; -- Wait 5 seconds
UPDATE Students SET Email = '<EMAIL>' WHERE StudentID = 1;
COMMIT;
*/

-- SQL Server will detect the deadlock and kill one of the transactions

-- =============================================
-- 7. DEADLOCK PREVENTION STRATEGIES
-- =============================================

-- Strategy 1: Always access tables in the same order
BEGIN TRANSACTION;
-- Always update Students first, then Courses
UPDATE Students SET Email = '<EMAIL>' WHERE StudentID = 1;
UPDATE Courses SET Name = 'Consistent Update' WHERE CourseID = 1;
COMMIT;

-- Strategy 2: Use shorter transactions
BEGIN TRANSACTION;
UPDATE Students SET Email = '<EMAIL>' WHERE StudentID = 1;
COMMIT; -- Commit quickly

BEGIN TRANSACTION;
UPDATE Courses SET Name = 'Short Update' WHERE CourseID = 1;
COMMIT; -- Separate transaction

-- Strategy 3: Use appropriate isolation levels
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
-- Lower isolation levels reduce lock duration

-- =============================================
-- 8. MONITORING TRANSACTIONS
-- =============================================

-- Check current transactions
SELECT 
    session_id,
    transaction_id,
    name,
    transaction_begin_time,
    transaction_type,
    transaction_state
FROM sys.dm_tran_session_transactions st
JOIN sys.dm_tran_active_transactions at ON st.transaction_id = at.transaction_id;

-- Check current locks
SELECT 
    resource_type,
    resource_database_id,
    resource_description,
    request_mode,
    request_type,
    request_status,
    request_session_id
FROM sys.dm_tran_locks
WHERE resource_database_id = DB_ID('UNIVERSITY');

-- Check for blocking
SELECT 
    blocking_session_id,
    session_id,
    wait_type,
    wait_time,
    wait_resource
FROM sys.dm_exec_requests
WHERE blocking_session_id <> 0;

-- =============================================
-- 9. PRACTICAL TRANSACTION EXAMPLES
-- =============================================

-- Example 1: Transfer student between departments
CREATE PROCEDURE sp_TransferStudent
    @StudentID INT,
    @NewDepartmentID INT
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Verify student exists
        IF NOT EXISTS (SELECT 1 FROM Students WHERE StudentID = @StudentID)
        BEGIN
            RAISERROR('Student not found', 16, 1);
            RETURN;
        END
        
        -- Verify department exists
        IF NOT EXISTS (SELECT 1 FROM Departments WHERE DepartmentID = @NewDepartmentID)
        BEGIN
            RAISERROR('Department not found', 16, 1);
            RETURN;
        END
        
        -- Update student's department
        UPDATE Students 
        SET DepartmentID = @NewDepartmentID
        WHERE StudentID = @StudentID;
        
        -- Log the transfer (if we had a log table)
        -- INSERT INTO StudentTransferLog...
        
        COMMIT TRANSACTION;
        PRINT 'Student transferred successfully';
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END;

-- Example 2: Batch grade entry with validation
CREATE PROCEDURE sp_BatchGradeEntry
    @CourseID INT
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @ProcessedCount INT = 0;
        DECLARE @ErrorCount INT = 0;
        
        -- Cursor for processing each enrolled student
        DECLARE grade_cursor CURSOR FOR
        SELECT StudentID FROM Enrollments WHERE CourseID = @CourseID;
        
        DECLARE @StudentID INT;
        OPEN grade_cursor;
        
        FETCH NEXT FROM grade_cursor INTO @StudentID;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            -- Simulate grade calculation (in real scenario, this might come from parameters)
            DECLARE @CalculatedGrade DECIMAL(3,1) = 3.0 + (RAND() * 1.0); -- Random grade between 3.0-4.0
            
            -- Insert or update grade
            IF EXISTS (SELECT 1 FROM Grades WHERE StudentID = @StudentID AND CourseID = @CourseID)
            BEGIN
                UPDATE Grades 
                SET Grade = @CalculatedGrade
                WHERE StudentID = @StudentID AND CourseID = @CourseID;
            END
            ELSE
            BEGIN
                INSERT INTO Grades (StudentID, CourseID, Grade)
                VALUES (@StudentID, @CourseID, @CalculatedGrade);
            END
            
            SET @ProcessedCount = @ProcessedCount + 1;
            
            FETCH NEXT FROM grade_cursor INTO @StudentID;
        END
        
        CLOSE grade_cursor;
        DEALLOCATE grade_cursor;
        
        COMMIT TRANSACTION;
        PRINT 'Batch grade entry completed. Processed: ' + CAST(@ProcessedCount AS VARCHAR) + ' students';
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        IF CURSOR_STATUS('global', 'grade_cursor') >= 0
        BEGIN
            CLOSE grade_cursor;
            DEALLOCATE grade_cursor;
        END
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END;

-- =============================================
-- 10. TRANSACTION BEST PRACTICES
-- =============================================

/*
TRANSACTION BEST PRACTICES:

1. Keep transactions as short as possible
2. Don't include user interaction in transactions
3. Access objects in the same order to prevent deadlocks
4. Use appropriate isolation levels
5. Always include error handling
6. Use savepoints for complex transactions
7. Avoid nested transactions when possible
8. Monitor for blocking and deadlocks
9. Test transaction logic thoroughly
10. Document transaction boundaries clearly

ACID PROPERTIES REMINDER:
- Atomicity: All or nothing
- Consistency: Database remains in valid state
- Isolation: Transactions don't interfere with each other
- Durability: Committed changes are permanent

COMMON ISOLATION LEVEL ISSUES:
- Dirty Read: Reading uncommitted data
- Non-repeatable Read: Same query returns different results
- Phantom Read: New rows appear between reads
- Lost Update: Concurrent updates overwrite each other
*/

-- Example usage:
-- EXEC sp_TransferStudent @StudentID = 1, @NewDepartmentID = 2;
-- EXEC sp_BatchGradeEntry @CourseID = 1;
