# 🎓 University Course Management System
## **ENHANCED FUNDAMENTAL LEVEL**

A comprehensive SQL Server database system designed for learning SQL from basic to intermediate concepts. Perfect for beginners who want to master fundamental database operations and progress to more advanced topics like stored procedures, transactions, and security.

## 📋 Table of Contents

- [Learning Objectives](#learning-objectives)
- [Database Schema](#database-schema)
- [Setup Instructions](#setup-instructions)
- [File Structure](#file-structure)
- [Features Covered](#features-covered)
- [Learning Path](#learning-path)
- [Practice Exercises](#practice-exercises)

## 🎯 Learning Objectives

This enhanced system teaches you:

### **Fundamental Level:**
- **Basic SQL Operations**: SELECT, INSERT, UPDATE, DELETE
- **Table Relationships**: Primary keys, foreign keys, referential integrity
- **JOIN Operations**: INNER JOIN, LEFT JOIN, FULL OUTER JOIN
- **Aggregate Functions**: COUNT, AVG, SUM, MIN, MAX
- **Grouping and Filtering**: GROUP BY, HAVING, WHERE clauses

### **Intermediate Level:**
- **Stored Procedures**: T-SQL programming with variables and logic
- **Advanced Queries**: Subqueries, CTEs, window functions
- **Transaction Management**: ACID properties, isolation levels
- **Security Concepts**: User management, permissions, encryption
- **Data Protection**: Backup strategies, recovery procedures

## 🗄️ Database Schema

### Core Tables (6 Tables)

| Table | Description | Key Learning Concept |
|-------|-------------|---------------------|
| `Departments` | Academic departments | Primary keys, basic structure |
| `Students` | Student information | Foreign keys, relationships |
| `Lecturers` | Faculty members | One-to-many relationships |
| `Courses` | Academic courses | Multiple foreign keys |
| `Enrollments` | Student-course relationships | Many-to-many relationships |
| `Grades` | Student grades | Composite primary keys |

### Sample Data
- **3 Departments**: Computer Science, Mathematics, Business
- **6 Students**: Realistic names and information
- **3 Lecturers**: One per department
- **6 Courses**: Two per department
- **12 Enrollments**: Various enrollment patterns
- **6 Grades**: Some students have grades, others don't

## 🚀 Setup Instructions

### Prerequisites
- Microsoft SQL Server (2016 or later)
- SQL Server Management Studio (SSMS) or Azure Data Studio

### Quick Setup (4 Steps)

1. **Create Database Schema**
   ```sql
   -- Run first: Creates tables and relationships
   unicourse.sql
   ```

2. **Add Sample Data**
   ```sql
   -- Run second: Populates tables with test data
   sample_data.sql
   ```

3. **Create Views**
   ```sql
   -- Run third: Creates helpful views
   views_and_procedures.sql
   ```

4. **Test Setup**
   ```sql
   -- Run fourth: Verifies everything works
   setup_and_test.sql
   ```

### Optional Advanced Features

5. **Add Stored Procedures**
   ```sql
   -- Learn T-SQL programming concepts
   basic_procedures.sql
   ```

6. **Practice Advanced Queries**
   ```sql
   -- Subqueries, CTEs, window functions
   advanced_queries.sql
   ```

7. **Learn Security**
   ```sql
   -- User management, backup, encryption
   security_and_backup.sql
   ```

8. **Understand Transactions**
   ```sql
   -- ACID properties, concurrency control
   transactions_and_concurrency.sql
   ```

## 📁 File Structure

```
sqlproject/
├── 📄 unicourse.sql                    # Database schema
├── 📄 sample_data.sql                 # Sample data
├── 📄 query_examples.sql              # Basic SQL queries
├── 📄 views_and_procedures.sql        # Simple views
├── 📄 setup_and_test.sql             # Setup verification
├── 📄 basic_procedures.sql           # T-SQL programming
├── 📄 advanced_queries.sql           # Advanced SQL features
├── 📄 security_and_backup.sql        # Security & backup
├── 📄 transactions_and_concurrency.sql # Transaction concepts
└── 📄 README.md                      # This documentation
```

## ✨ Features Covered

### **Core SQL (Fundamental)**
- ✅ Table creation and constraints
- ✅ Basic CRUD operations
- ✅ JOIN operations (INNER, LEFT, FULL OUTER)
- ✅ Aggregate functions and GROUP BY
- ✅ Simple views and basic reporting

### **T-SQL Programming (Intermediate)**
- ✅ Variables and data types
- ✅ IF...ELSE conditional logic
- ✅ CASE statements
- ✅ Stored procedures with parameters
- ✅ Error handling with TRY...CATCH

### **Advanced Querying**
- ✅ Subqueries (correlated and non-correlated)
- ✅ Common Table Expressions (CTEs)
- ✅ Window functions (ROW_NUMBER, RANK, LAG/LEAD)
- ✅ Advanced JOIN techniques
- ✅ Complex aggregations (ROLLUP, CUBE)

### **Database Security**
- ✅ User management and authentication
- ✅ Role-based access control
- ✅ Basic encryption concepts
- ✅ Audit and monitoring
- ✅ Security best practices

### **Data Protection**
- ✅ Backup strategies (Full, Differential, Log)
- ✅ Restore procedures
- ✅ Point-in-time recovery
- ✅ Database integrity checks
- ✅ Disaster recovery planning

### **Transaction Management**
- ✅ ACID properties understanding
- ✅ Transaction control (BEGIN, COMMIT, ROLLBACK)
- ✅ Isolation levels
- ✅ Deadlock prevention
- ✅ Concurrency control

## 🛤️ Learning Path

### **Phase 1: Fundamentals (Start Here)**
1. Run `unicourse.sql` and `sample_data.sql`
2. Practice with `query_examples.sql`
3. Understand views with `views_and_procedures.sql`
4. Verify setup with `setup_and_test.sql`

### **Phase 2: Programming Concepts**
1. Learn T-SQL with `basic_procedures.sql`
2. Practice stored procedures and functions
3. Understand variables and control flow

### **Phase 3: Advanced Querying**
1. Master subqueries with `advanced_queries.sql`
2. Learn CTEs and window functions
3. Practice complex reporting queries

### **Phase 4: Database Administration**
1. Study security with `security_and_backup.sql`
2. Learn backup and recovery procedures
3. Understand user management

### **Phase 5: Transaction Processing**
1. Master transactions with `transactions_and_concurrency.sql`
2. Understand ACID properties
3. Learn concurrency control

## 💡 Practice Exercises

### **Beginner Exercises**
1. List all students in Computer Science department
2. Show courses with more than 3 credits
3. Calculate average GPA by department
4. Find students without grades

### **Intermediate Exercises**
1. Create a stored procedure to enroll students
2. Write a CTE to rank students by GPA
3. Use window functions to show running totals
4. Implement error handling in procedures

### **Advanced Exercises**
1. Create a comprehensive backup strategy
2. Implement row-level security
3. Design a transaction for complex operations
4. Build a complete reporting system

## 🎓 Why This Enhanced System?

### **Progressive Learning**
- Start with basics, advance gradually
- Each file builds on previous concepts
- Clear learning objectives for each phase

### **Real-World Relevance**
- University domain everyone understands
- Practical business scenarios
- Industry-standard practices

### **Comprehensive Coverage**
- From basic SELECT to advanced transactions
- Security and backup procedures
- Performance and optimization concepts

### **Hands-On Practice**
- Realistic sample data
- Practical exercises
- Real-world scenarios

## 🔧 Advanced Features

### **Stored Procedures Include:**
- Parameter validation
- Error handling
- Business logic implementation
- Reporting procedures

### **Security Features Include:**
- User role management
- Data encryption examples
- Audit trail implementation
- Backup automation

### **Transaction Features Include:**
- ACID property demonstrations
- Deadlock prevention strategies
- Isolation level examples
- Concurrency control patterns

## 📚 Next Steps

After mastering this system:
1. **Performance Tuning**: Learn indexing and optimization
2. **Advanced Security**: Study encryption and compliance
3. **Business Intelligence**: Explore reporting and analytics
4. **Cloud Databases**: Transition to Azure SQL or AWS RDS
5. **NoSQL Concepts**: Understand document and graph databases

---

**🎯 Start with the fundamentals and progress at your own pace. Each file is designed to build your SQL expertise step by step!**

**Happy Learning! 🎓**
