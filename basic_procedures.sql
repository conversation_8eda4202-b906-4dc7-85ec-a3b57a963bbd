-- =============================================
-- BASIC STORED PROCEDURES - FUNDAMENTAL LEVEL
-- =============================================
-- Simple stored procedures for learning basic T-SQL programming concepts
-- These introduce variables, IF...ELSE, and basic error handling

USE UNIVERSITY;

-- =============================================
-- 1. SIMPLE STORED PROCEDURE - ENROLL STUDENT
-- =============================================

CREATE PROCEDURE sp_EnrollStudent
    @StudentID INT,
    @CourseID INT
AS
BEGIN
    -- Declare variables (T-SQL concept)
    DECLARE @StudentExists BIT = 0;
    DECLARE @CourseExists BIT = 0;
    DECLARE @AlreadyEnrolled BIT = 0;
    DECLARE @StudentName NVARCHAR(100);
    DECLARE @CourseName NVARCHAR(100);
    
    -- Check if student exists (IF...ELSE concept)
    IF EXISTS (SELECT 1 FROM Students WHERE StudentID = @StudentID)
    BEGIN
        SET @StudentExists = 1;
        SELECT @StudentName = FirstName + ' ' + LastName 
        FROM Students WHERE StudentID = @StudentID;
    END
    
    -- Check if course exists
    IF EXISTS (SELECT 1 FROM Courses WHERE CourseID = @CourseID)
    BEGIN
        SET @CourseExists = 1;
        SELECT @CourseName = Code + ' - ' + Name 
        FROM Courses WHERE CourseID = @CourseID;
    END
    
    -- Check if already enrolled
    IF EXISTS (SELECT 1 FROM Enrollments WHERE StudentID = @StudentID AND CourseID = @CourseID)
    BEGIN
        SET @AlreadyEnrolled = 1;
    END
    
    -- Business logic with IF...ELSE
    IF @StudentExists = 0
    BEGIN
        PRINT 'Error: Student ID ' + CAST(@StudentID AS VARCHAR) + ' does not exist.';
        RETURN;
    END
    
    IF @CourseExists = 0
    BEGIN
        PRINT 'Error: Course ID ' + CAST(@CourseID AS VARCHAR) + ' does not exist.';
        RETURN;
    END
    
    IF @AlreadyEnrolled = 1
    BEGIN
        PRINT 'Error: ' + @StudentName + ' is already enrolled in ' + @CourseName;
        RETURN;
    END
    
    -- Perform the enrollment
    INSERT INTO Enrollments (StudentID, CourseID, EnrollmentDate)
    VALUES (@StudentID, @CourseID, GETDATE());
    
    PRINT 'Success: ' + @StudentName + ' has been enrolled in ' + @CourseName;
END;

-- =============================================
-- 2. STORED PROCEDURE WITH CASE STATEMENT
-- =============================================

CREATE PROCEDURE sp_AddGradeWithValidation
    @StudentID INT,
    @CourseID INT,
    @Grade DECIMAL(3,1)
AS
BEGIN
    DECLARE @IsEnrolled BIT = 0;
    DECLARE @GradeCategory NVARCHAR(20);
    DECLARE @StudentName NVARCHAR(100);
    DECLARE @CourseName NVARCHAR(100);
    
    -- Check enrollment
    IF EXISTS (SELECT 1 FROM Enrollments WHERE StudentID = @StudentID AND CourseID = @CourseID)
    BEGIN
        SET @IsEnrolled = 1;
        
        -- Get names for feedback
        SELECT @StudentName = FirstName + ' ' + LastName 
        FROM Students WHERE StudentID = @StudentID;
        
        SELECT @CourseName = Code + ' - ' + Name 
        FROM Courses WHERE CourseID = @CourseID;
    END
    
    -- Validate enrollment
    IF @IsEnrolled = 0
    BEGIN
        PRINT 'Error: Student must be enrolled in the course before receiving a grade.';
        RETURN;
    END
    
    -- Validate grade range
    IF @Grade < 0.0 OR @Grade > 4.0
    BEGIN
        PRINT 'Error: Grade must be between 0.0 and 4.0';
        RETURN;
    END
    
    -- CASE statement to categorize grade (T-SQL concept)
    SET @GradeCategory = 
        CASE 
            WHEN @Grade >= 3.7 THEN 'A (Excellent)'
            WHEN @Grade >= 3.3 THEN 'B+ (Good)'
            WHEN @Grade >= 3.0 THEN 'B (Satisfactory)'
            WHEN @Grade >= 2.7 THEN 'C+ (Below Average)'
            WHEN @Grade >= 2.0 THEN 'C (Poor)'
            ELSE 'F (Failing)'
        END;
    
    -- Insert or update grade
    IF EXISTS (SELECT 1 FROM Grades WHERE StudentID = @StudentID AND CourseID = @CourseID)
    BEGIN
        UPDATE Grades 
        SET Grade = @Grade
        WHERE StudentID = @StudentID AND CourseID = @CourseID;
        
        PRINT 'Grade updated: ' + @StudentName + ' received ' + CAST(@Grade AS VARCHAR) + 
              ' (' + @GradeCategory + ') in ' + @CourseName;
    END
    ELSE
    BEGIN
        INSERT INTO Grades (StudentID, CourseID, Grade)
        VALUES (@StudentID, @CourseID, @Grade);
        
        PRINT 'Grade added: ' + @StudentName + ' received ' + CAST(@Grade AS VARCHAR) + 
              ' (' + @GradeCategory + ') in ' + @CourseName;
    END
END;

-- =============================================
-- 3. REPORTING PROCEDURE WITH LOOPS
-- =============================================

CREATE PROCEDURE sp_GenerateStudentReport
    @StudentID INT
AS
BEGIN
    DECLARE @StudentName NVARCHAR(100);
    DECLARE @Department NVARCHAR(100);
    DECLARE @EnrollmentYear INT;
    DECLARE @TotalCourses INT;
    DECLARE @CompletedCourses INT;
    DECLARE @GPA DECIMAL(3,2);
    
    -- Check if student exists
    IF NOT EXISTS (SELECT 1 FROM Students WHERE StudentID = @StudentID)
    BEGIN
        PRINT 'Error: Student ID ' + CAST(@StudentID AS VARCHAR) + ' not found.';
        RETURN;
    END
    
    -- Get student information
    SELECT 
        @StudentName = S.FirstName + ' ' + S.LastName,
        @Department = D.Name,
        @EnrollmentYear = S.EnrollmentYear
    FROM Students S
    JOIN Departments D ON S.DepartmentID = D.DepartmentID
    WHERE S.StudentID = @StudentID;
    
    -- Calculate statistics
    SELECT @TotalCourses = COUNT(*)
    FROM Enrollments
    WHERE StudentID = @StudentID;
    
    SELECT 
        @CompletedCourses = COUNT(*),
        @GPA = AVG(Grade)
    FROM Grades
    WHERE StudentID = @StudentID;
    
    -- Set defaults for NULL values
    SET @CompletedCourses = ISNULL(@CompletedCourses, 0);
    SET @GPA = ISNULL(@GPA, 0.00);
    
    -- Display report header
    PRINT '==========================================';
    PRINT 'STUDENT ACADEMIC REPORT';
    PRINT '==========================================';
    PRINT 'Student: ' + @StudentName;
    PRINT 'Department: ' + @Department;
    PRINT 'Enrollment Year: ' + CAST(@EnrollmentYear AS VARCHAR);
    PRINT 'Total Courses Enrolled: ' + CAST(@TotalCourses AS VARCHAR);
    PRINT 'Courses Completed: ' + CAST(@CompletedCourses AS VARCHAR);
    PRINT 'Current GPA: ' + CAST(@GPA AS VARCHAR);
    
    -- Academic standing using CASE
    DECLARE @AcademicStanding NVARCHAR(50);
    SET @AcademicStanding = 
        CASE 
            WHEN @GPA >= 3.7 THEN 'Dean''s List'
            WHEN @GPA >= 3.5 THEN 'Honor Roll'
            WHEN @GPA >= 3.0 THEN 'Good Standing'
            WHEN @GPA >= 2.0 THEN 'Academic Warning'
            WHEN @CompletedCourses = 0 THEN 'No Grades Yet'
            ELSE 'Academic Probation'
        END;
    
    PRINT 'Academic Standing: ' + @AcademicStanding;
    PRINT '==========================================';
    
    -- Show detailed course information
    SELECT 
        C.Code,
        C.Name AS CourseName,
        C.Credits,
        CASE 
            WHEN G.Grade IS NULL THEN 'In Progress'
            ELSE CAST(G.Grade AS VARCHAR)
        END AS Grade,
        CASE 
            WHEN G.Grade IS NULL THEN 'N/A'
            WHEN G.Grade >= 3.7 THEN 'A'
            WHEN G.Grade >= 3.3 THEN 'B+'
            WHEN G.Grade >= 3.0 THEN 'B'
            WHEN G.Grade >= 2.7 THEN 'C+'
            WHEN G.Grade >= 2.0 THEN 'C'
            ELSE 'F'
        END AS LetterGrade
    FROM Enrollments E
    JOIN Courses C ON E.CourseID = C.CourseID
    LEFT JOIN Grades G ON E.StudentID = G.StudentID AND E.CourseID = G.CourseID
    WHERE E.StudentID = @StudentID
    ORDER BY C.Code;
END;

-- =============================================
-- USAGE EXAMPLES
-- =============================================

-- Example 1: Enroll a student
-- EXEC sp_EnrollStudent @StudentID = 1, @CourseID = 4;

-- Example 2: Add a grade
-- EXEC sp_AddGradeWithValidation @StudentID = 1, @CourseID = 4, @Grade = 3.5;

-- Example 3: Generate student report
-- EXEC sp_GenerateStudentReport @StudentID = 1;
