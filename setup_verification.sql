-- =============================================
-- UNIVERSITY COURSE MANAGEMENT SYSTEM
-- SETUP VERIFICATION AND TESTING SCRIPT
-- =============================================
-- Purpose: Verify database creation, data population, and relationships
-- Compatible with: SQL Server Management Studio (SSMS)
-- Run this AFTER: unicourse.sql and sample_data.sql

USE UNIVERSITY;
GO

PRINT '=== UNIVERSITY COURSE MANAGEMENT SYSTEM VERIFICATION ===';
PRINT 'Timestamp: ' + CONVERT(NVARCHAR(30), GETDATE(), 120);
PRINT '';

-- =============================================
-- 1. DATABASE AND TABLE EXISTENCE CHECK
-- =============================================
PRINT '1. VERIFYING DATABASE STRUCTURE...';

-- Check if all tables exist
IF OBJECT_ID('Departments', 'U') IS NOT NULL
    PRINT '   ✓ DEPARTMENTS table exists'
ELSE
    PRINT '   ✗ DEPARTMENTS table missing - Run unicourse.sql first';

IF OBJECT_ID('Lecturers', 'U') IS NOT NULL
    PRINT '   ✓ LECTURERS table exists'
ELSE
    PRINT '   ✗ LECTURERS table missing - Run unicourse.sql first';

IF OBJECT_ID('Students', 'U') IS NOT NULL
    PRINT '   ✓ STUDENTS table exists'
ELSE
    PRINT '   ✗ STUDENTS table missing - Run unicourse.sql first';

IF OBJECT_ID('Courses', 'U') IS NOT NULL
    PRINT '   ✓ COURSES table exists'
ELSE
    PRINT '   ✗ COURSES table missing - Run unicourse.sql first';

IF OBJECT_ID('Enrollments', 'U') IS NOT NULL
    PRINT '   ✓ ENROLLMENTS table exists'
ELSE
    PRINT '   ✗ ENROLLMENTS table missing - Run unicourse.sql first';

IF OBJECT_ID('Grades', 'U') IS NOT NULL
    PRINT '   ✓ GRADES table exists'
ELSE
    PRINT '   ✗ GRADES table missing - Run unicourse.sql first';

PRINT '';

-- =============================================
-- 2. DATA POPULATION VERIFICATION
-- =============================================
PRINT '2. VERIFYING SAMPLE DATA POPULATION...';

DECLARE @DeptCount INT = (SELECT COUNT(*) FROM Departments);
DECLARE @LecturerCount INT = (SELECT COUNT(*) FROM Lecturers);
DECLARE @StudentCount INT = (SELECT COUNT(*) FROM Students);
DECLARE @CourseCount INT = (SELECT COUNT(*) FROM Courses);
DECLARE @EnrollmentCount INT = (SELECT COUNT(*) FROM Enrollments);
DECLARE @GradeCount INT = (SELECT COUNT(*) FROM Grades);

PRINT '   Departments: ' + CAST(@DeptCount AS NVARCHAR(10)) + ' records';
PRINT '   Lecturers: ' + CAST(@LecturerCount AS NVARCHAR(10)) + ' records';
PRINT '   Students: ' + CAST(@StudentCount AS NVARCHAR(10)) + ' records';
PRINT '   Courses: ' + CAST(@CourseCount AS NVARCHAR(10)) + ' records';
PRINT '   Enrollments: ' + CAST(@EnrollmentCount AS NVARCHAR(10)) + ' records';
PRINT '   Grades: ' + CAST(@GradeCount AS NVARCHAR(10)) + ' records';

-- Verify minimum expected data
IF @DeptCount >= 5 AND @LecturerCount >= 8 AND @StudentCount >= 12 
   AND @CourseCount >= 18 AND @EnrollmentCount >= 35 AND @GradeCount >= 20
    PRINT '   ✓ Sample data population looks good!';
ELSE
    PRINT '   ⚠ Sample data may be incomplete - Run sample_data.sql';

PRINT '';

-- =============================================
-- 3. FOREIGN KEY RELATIONSHIP VERIFICATION
-- =============================================
PRINT '3. VERIFYING FOREIGN KEY RELATIONSHIPS...';

-- Check for orphaned records (referential integrity)
DECLARE @OrphanLecturers INT = (
    SELECT COUNT(*) FROM Lecturers l 
    WHERE NOT EXISTS (SELECT 1 FROM Departments d WHERE d.DepartmentID = l.DepartmentID)
);

DECLARE @OrphanStudents INT = (
    SELECT COUNT(*) FROM Students s 
    WHERE NOT EXISTS (SELECT 1 FROM Departments d WHERE d.DepartmentID = s.DepartmentID)
);

DECLARE @OrphanCourses INT = (
    SELECT COUNT(*) FROM Courses c 
    WHERE NOT EXISTS (SELECT 1 FROM Departments d WHERE d.DepartmentID = c.DepartmentID)
       OR NOT EXISTS (SELECT 1 FROM Lecturers l WHERE l.LecturerID = c.LecturerID)
);

DECLARE @OrphanEnrollments INT = (
    SELECT COUNT(*) FROM Enrollments e 
    WHERE NOT EXISTS (SELECT 1 FROM Students s WHERE s.StudentID = e.StudentID)
       OR NOT EXISTS (SELECT 1 FROM Courses c WHERE c.CourseID = e.CourseID)
);

DECLARE @OrphanGrades INT = (
    SELECT COUNT(*) FROM Grades g 
    WHERE NOT EXISTS (SELECT 1 FROM Students s WHERE s.StudentID = g.StudentID)
       OR NOT EXISTS (SELECT 1 FROM Courses c WHERE c.CourseID = g.CourseID)
);

IF @OrphanLecturers = 0 AND @OrphanStudents = 0 AND @OrphanCourses = 0 
   AND @OrphanEnrollments = 0 AND @OrphanGrades = 0
    PRINT '   ✓ All foreign key relationships are valid';
ELSE
BEGIN
    PRINT '   ⚠ Found referential integrity issues:';
    IF @OrphanLecturers > 0 PRINT '     - ' + CAST(@OrphanLecturers AS NVARCHAR(10)) + ' orphaned lecturers';
    IF @OrphanStudents > 0 PRINT '     - ' + CAST(@OrphanStudents AS NVARCHAR(10)) + ' orphaned students';
    IF @OrphanCourses > 0 PRINT '     - ' + CAST(@OrphanCourses AS NVARCHAR(10)) + ' orphaned courses';
    IF @OrphanEnrollments > 0 PRINT '     - ' + CAST(@OrphanEnrollments AS NVARCHAR(10)) + ' orphaned enrollments';
    IF @OrphanGrades > 0 PRINT '     - ' + CAST(@OrphanGrades AS NVARCHAR(10)) + ' orphaned grades';
END

PRINT '';

-- =============================================
-- 4. CONSTRAINT VERIFICATION
-- =============================================
PRINT '4. VERIFYING DATA CONSTRAINTS...';

-- Check email uniqueness
DECLARE @DuplicateEmails INT = (
    SELECT COUNT(*) FROM (
        SELECT Email FROM Students
        UNION ALL
        SELECT Email FROM Lecturers
    ) AS AllEmails
    GROUP BY Email
    HAVING COUNT(*) > 1
);

-- Check grade ranges
DECLARE @InvalidGrades INT = (
    SELECT COUNT(*) FROM Grades 
    WHERE Grade < 0.0 OR Grade > 4.0
);

-- Check credit ranges
DECLARE @InvalidCredits INT = (
    SELECT COUNT(*) FROM Courses 
    WHERE Credits < 1 OR Credits > 6
);

-- Check enrollment years
DECLARE @InvalidYears INT = (
    SELECT COUNT(*) FROM Students 
    WHERE EnrollmentYear < 2020 OR EnrollmentYear > YEAR(GETDATE()) + 1
);

IF @DuplicateEmails = 0 AND @InvalidGrades = 0 AND @InvalidCredits = 0 AND @InvalidYears = 0
    PRINT '   ✓ All data constraints are satisfied';
ELSE
BEGIN
    PRINT '   ⚠ Found constraint violations:';
    IF @DuplicateEmails > 0 PRINT '     - ' + CAST(@DuplicateEmails AS NVARCHAR(10)) + ' duplicate email addresses';
    IF @InvalidGrades > 0 PRINT '     - ' + CAST(@InvalidGrades AS NVARCHAR(10)) + ' grades outside 0.0-4.0 range';
    IF @InvalidCredits > 0 PRINT '     - ' + CAST(@InvalidCredits AS NVARCHAR(10)) + ' courses with invalid credit hours';
    IF @InvalidYears > 0 PRINT '     - ' + CAST(@InvalidYears AS NVARCHAR(10)) + ' students with invalid enrollment years';
END

PRINT '';

-- =============================================
-- 5. SAMPLE QUERY VERIFICATION
-- =============================================
PRINT '5. TESTING SAMPLE QUERIES...';

-- Test basic SELECT
BEGIN TRY
    SELECT TOP 1 * FROM Departments;
    PRINT '   ✓ Basic SELECT query works';
EXCEPTION
    PRINT '   ✗ Basic SELECT query failed';
END TRY

-- Test JOIN operation
BEGIN TRY
    SELECT TOP 1 s.FirstName, d.Name 
    FROM Students s 
    INNER JOIN Departments d ON s.DepartmentID = d.DepartmentID;
    PRINT '   ✓ JOIN query works';
EXCEPTION
    PRINT '   ✗ JOIN query failed';
END TRY

-- Test aggregate function
BEGIN TRY
    SELECT COUNT(*) FROM Students;
    PRINT '   ✓ Aggregate function works';
EXCEPTION
    PRINT '   ✗ Aggregate function failed';
END TRY

PRINT '';

-- =============================================
-- 6. FINAL VERIFICATION SUMMARY
-- =============================================
PRINT '=== VERIFICATION COMPLETE ===';

IF @DeptCount >= 5 AND @LecturerCount >= 8 AND @StudentCount >= 12 
   AND @CourseCount >= 18 AND @EnrollmentCount >= 35 AND @GradeCount >= 20
   AND @OrphanLecturers = 0 AND @OrphanStudents = 0 AND @OrphanCourses = 0 
   AND @OrphanEnrollments = 0 AND @OrphanGrades = 0
   AND @DuplicateEmails = 0 AND @InvalidGrades = 0 AND @InvalidCredits = 0 AND @InvalidYears = 0
BEGIN
    PRINT '🎉 SUCCESS: University Course Management System is ready!';
    PRINT '';
    PRINT 'Next Steps:';
    PRINT '1. Try the sample queries in query_examples.sql';
    PRINT '2. Explore the relationships using JOINs';
    PRINT '3. Practice aggregate functions and GROUP BY';
    PRINT '4. Review the documentation for learning guidance';
END
ELSE
BEGIN
    PRINT '⚠ ISSUES FOUND: Please review the messages above';
    PRINT 'Ensure you have run both unicourse.sql and sample_data.sql';
END

PRINT '';
PRINT 'Happy SQL Learning! 🎓';

GO
