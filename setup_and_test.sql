-- Setup and Testing Script for University Course Management System
-- Run this script to set up the complete database and test functionality

-- =============================================
-- SETUP INSTRUCTIONS
-- =============================================
/*
1. First run: unicourse.sql (creates basic schema)
2. Then run: sample_data.sql (inserts sample data)
3. Then run: views_and_procedures.sql (creates views and procedures)
4. Then run: extensions.sql (adds advanced features)
5. Finally run this script to test everything
*/

USE UNIVERSITY;

-- =============================================
-- BASIC FUNCTIONALITY TESTS
-- =============================================

PRINT '=== TESTING BASIC FUNCTIONALITY ===';

-- Test 1: Verify all tables exist and have data
PRINT 'Test 1: Table Data Counts';
SELECT 'Departments' as TableName, COUNT(*) as RecordCount FROM Departments
UNION ALL
SELECT 'Lecturers', COUNT(*) FROM Lecturers
UNION ALL
SELECT 'Students', COUNT(*) FROM Students
UNION ALL
SELECT 'Courses', COUNT(*) FROM Courses
UNION ALL
SELECT 'Enrollments', COUNT(*) FROM Enrollments
UNION ALL
SELECT 'Grades', COUNT(*) FROM Grades
UNION ALL
SELECT 'CourseSchedule', COUNT(*) FROM CourseSchedule;

-- Test 2: Test Views
PRINT 'Test 2: Testing Views';
SELECT TOP 3 * FROM vw_StudentCourseSummary;
SELECT TOP 3 * FROM vw_DepartmentStats;

-- =============================================
-- STORED PROCEDURE TESTS
-- =============================================

PRINT '=== TESTING STORED PROCEDURES ===';

-- Test 3: Test Student Enrollment
PRINT 'Test 3: Testing Student Enrollment Procedure';
EXEC sp_EnrollStudent @StudentID = 2, @CourseID = 1; -- Should work
EXEC sp_EnrollStudent @StudentID = 2, @CourseID = 1; -- Should fail (already enrolled)
EXEC sp_EnrollStudent @StudentID = 999, @CourseID = 1; -- Should fail (student doesn't exist)

-- Test 4: Test Grade Addition
PRINT 'Test 4: Testing Grade Addition Procedure';
EXEC sp_AddGrade @StudentID = 2, @CourseID = 1, @Grade = 4.2; -- Should work
EXEC sp_AddGrade @StudentID = 2, @CourseID = 1, @Grade = 4.8; -- Should update existing grade
EXEC sp_AddGrade @StudentID = 1, @CourseID = 8, @Grade = 3.5; -- Should fail (not enrolled)

-- Test 5: Test Student Transcript
PRINT 'Test 5: Testing Student Transcript Procedure';
EXEC sp_GetStudentTranscript @StudentID = 1;

-- =============================================
-- ADVANCED FEATURE TESTS
-- =============================================

PRINT '=== TESTING ADVANCED FEATURES ===';

-- Test 6: Test GPA Function
PRINT 'Test 6: Testing GPA Calculation Function';
SELECT 
    StudentID,
    FirstName + ' ' + LastName AS StudentName,
    dbo.fn_CalculateGPA(StudentID) AS CalculatedGPA
FROM Students
WHERE StudentID IN (1, 2, 3);

-- Test 7: Test Prerequisites Function
PRINT 'Test 7: Testing Prerequisites Check Function';
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code + ' - ' + C.Name AS CourseName,
    CASE 
        WHEN dbo.fn_CheckPrerequisites(S.StudentID, C.CourseID) = 1 
        THEN 'Prerequisites Met' 
        ELSE 'Prerequisites NOT Met' 
    END AS PrerequisiteStatus
FROM Students S
CROSS JOIN Courses C
WHERE S.StudentID IN (1, 2) AND C.CourseID IN (2, 4, 6); -- Courses with prerequisites

-- Test 8: Test Academic Standing View
PRINT 'Test 8: Testing Academic Standing View';
SELECT * FROM vw_StudentAcademicStanding;

-- =============================================
-- BUSINESS LOGIC TESTS
-- =============================================

PRINT '=== TESTING BUSINESS LOGIC ===';

-- Test 9: Course Capacity Management
PRINT 'Test 9: Testing Course Capacity';
SELECT 
    C.Code,
    C.Name,
    CO.MaxEnrollment,
    CO.CurrentEnrollment,
    CO.MaxEnrollment - CO.CurrentEnrollment AS AvailableSpots,
    CO.Status
FROM Courses C
JOIN CourseOfferings CO ON C.CourseID = CO.CourseID;

-- Test 10: Attendance Analysis
PRINT 'Test 10: Testing Attendance Tracking';
SELECT 
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    COUNT(*) AS TotalClasses,
    SUM(CASE WHEN A.Status = 'Present' THEN 1 ELSE 0 END) AS PresentCount,
    SUM(CASE WHEN A.Status = 'Absent' THEN 1 ELSE 0 END) AS AbsentCount,
    CAST(SUM(CASE WHEN A.Status = 'Present' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) AS AttendanceRate
FROM Students S
JOIN Attendance A ON S.StudentID = A.StudentID
JOIN Courses C ON A.CourseID = C.CourseID
GROUP BY S.StudentID, S.FirstName, S.LastName, C.Code
ORDER BY AttendanceRate DESC;

-- =============================================
-- PERFORMANCE AND ANALYTICS QUERIES
-- =============================================

PRINT '=== PERFORMANCE AND ANALYTICS ===';

-- Test 11: Department Performance Analysis
PRINT 'Test 11: Department Performance Analysis';
SELECT 
    D.Name AS Department,
    COUNT(DISTINCT S.StudentID) AS TotalStudents,
    COUNT(DISTINCT G.StudentID) AS StudentsWithGrades,
    AVG(G.Grade) AS DepartmentGPA,
    COUNT(DISTINCT C.CourseID) AS CoursesOffered,
    AVG(CAST(C.Credits AS FLOAT)) AS AverageCredits
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
LEFT JOIN Grades G ON S.StudentID = G.StudentID
LEFT JOIN Courses C ON D.DepartmentID = C.DepartmentID
GROUP BY D.DepartmentID, D.Name
ORDER BY DepartmentGPA DESC;

-- Test 12: Course Popularity and Success Rate
PRINT 'Test 12: Course Popularity and Success Rate';
SELECT 
    C.Code,
    C.Name,
    COUNT(DISTINCT E.StudentID) AS TotalEnrollments,
    COUNT(DISTINCT G.StudentID) AS StudentsGraded,
    AVG(G.Grade) AS AverageGrade,
    SUM(CASE WHEN G.Grade >= 3.0 THEN 1 ELSE 0 END) AS PassingGrades,
    CASE 
        WHEN COUNT(G.Grade) > 0 
        THEN CAST(SUM(CASE WHEN G.Grade >= 3.0 THEN 1 ELSE 0 END) * 100.0 / COUNT(G.Grade) AS DECIMAL(5,2))
        ELSE 0 
    END AS PassRate
FROM Courses C
LEFT JOIN Enrollments E ON C.CourseID = E.CourseID
LEFT JOIN Grades G ON C.CourseID = G.CourseID
GROUP BY C.CourseID, C.Code, C.Name
ORDER BY TotalEnrollments DESC;

-- Test 13: Schedule Conflicts Detection
PRINT 'Test 13: Schedule Conflicts Detection';
SELECT 
    CS1.CourseID AS Course1,
    C1.Code AS Course1Code,
    CS2.CourseID AS Course2,
    C2.Code AS Course2Code,
    CS1.DayOfWeek,
    CS1.StartTime,
    CS1.EndTime,
    CS1.Room AS Room1,
    CS2.Room AS Room2
FROM CourseSchedule CS1
JOIN CourseSchedule CS2 ON CS1.DayOfWeek = CS2.DayOfWeek 
    AND CS1.ScheduleID < CS2.ScheduleID
    AND (
        (CS1.StartTime <= CS2.StartTime AND CS1.EndTime > CS2.StartTime) OR
        (CS2.StartTime <= CS1.StartTime AND CS2.EndTime > CS1.StartTime)
    )
JOIN Courses C1 ON CS1.CourseID = C1.CourseID
JOIN Courses C2 ON CS2.CourseID = C2.CourseID
ORDER BY CS1.DayOfWeek, CS1.StartTime;

PRINT '=== ALL TESTS COMPLETED ===';
PRINT 'Database setup and testing completed successfully!';
PRINT 'You can now use the University Course Management System.';

-- =============================================
-- SAMPLE USAGE EXAMPLES
-- =============================================

PRINT '=== SAMPLE USAGE EXAMPLES ===';

-- Example 1: Enroll a new student in multiple courses
PRINT 'Example 1: Enrolling student in courses';
-- EXEC sp_EnrollStudent @StudentID = 3, @CourseID = 1;
-- EXEC sp_EnrollStudent @StudentID = 3, @CourseID = 3;

-- Example 2: Add grades for students
PRINT 'Example 2: Adding grades';
-- EXEC sp_AddGrade @StudentID = 3, @CourseID = 1, @Grade = 3.8;
-- EXEC sp_AddGrade @StudentID = 3, @CourseID = 3, @Grade = 4.2;

-- Example 3: Generate reports
PRINT 'Example 3: Generate student transcript';
-- EXEC sp_GetStudentTranscript @StudentID = 1;

PRINT 'Setup complete! The database is ready for use.';
