-- =============================================
-- BASIC SETUP AND TESTING - FUNDAMENTAL LEVEL
-- =============================================
-- Simple tests to verify your database is working correctly

-- =============================================
-- SETUP INSTRUCTIONS
-- =============================================
/*
To set up the complete fundamental database system:

1. First run: unicourse.sql (creates the database schema)
2. Then run: sample_data.sql (adds sample data)
3. Then run: views_and_procedures.sql (creates simple views)
4. Finally run this script to test everything

Each file should be run in SQL Server Management Studio or similar tool.
*/

USE UNIVERSITY;

-- =============================================
-- BASIC TESTS
-- =============================================

PRINT '=== TESTING FUNDAMENTAL DATABASE ===';

-- Test 1: Check if all tables have data
PRINT 'Test 1: Checking table data counts...';
SELECT 'Departments' as TableName, COUNT(*) as RecordCount FROM Departments
UNION ALL
SELECT 'Students', COUNT(*) FROM Students
UNION ALL
SELECT 'Lecturers', COUNT(*) FROM Lecturers
UNION ALL
SELECT 'Courses', COUNT(*) FROM Courses
UNION ALL
SELECT 'Enrollments', COUNT(*) FROM Enrollments
UNION ALL
SELECT 'Grades', COUNT(*) FROM Grades;

-- Test 2: Test basic queries
PRINT 'Test 2: Testing basic SELECT queries...';

-- Show all students
SELECT 'All Students:' as Test;
SELECT FirstName, LastName, EnrollmentYear FROM Students;

-- Show students with departments
SELECT 'Students with Departments:' as Test;
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    D.Name AS Department
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID;

-- Test 3: Test views
PRINT 'Test 3: Testing views...';

-- Test student info view
SELECT 'Student Info View:' as Test;
SELECT TOP 3 * FROM vw_StudentInfo;

-- Test course catalog view
SELECT 'Course Catalog View:' as Test;
SELECT * FROM vw_CourseCatalog;

-- Test grades view
SELECT 'Student Grades View:' as Test;
SELECT * FROM vw_StudentGrades;

-- Test 4: Test aggregation queries
PRINT 'Test 4: Testing GROUP BY queries...';

-- Count students by department
SELECT 'Students by Department:' as Test;
SELECT
    D.Name AS Department,
    COUNT(S.StudentID) AS StudentCount
FROM Departments D
LEFT JOIN Students S ON D.DepartmentID = S.DepartmentID
GROUP BY D.Name;

-- Average grade by course
SELECT 'Average Grades by Course:' as Test;
SELECT
    C.Code,
    C.Name AS CourseName,
    AVG(G.Grade) AS AverageGrade,
    COUNT(G.Grade) AS NumberOfGrades
FROM Courses C
LEFT JOIN Grades G ON C.CourseID = G.CourseID
GROUP BY C.Code, C.Name
ORDER BY AverageGrade DESC;

-- Test 5: Test JOIN operations
PRINT 'Test 5: Testing JOIN operations...';

-- Students with their enrolled courses
SELECT 'Student Enrollments:' as Test;
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code + ' - ' + C.Name AS Course
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
ORDER BY S.LastName;

-- Students with grades (INNER JOIN - only students with grades)
SELECT 'Students with Grades (INNER JOIN):' as Test;
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    G.Grade
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
JOIN Courses C ON G.CourseID = C.CourseID
ORDER BY G.Grade DESC;

-- Students without grades (LEFT JOIN - shows all enrollments)
SELECT 'Students without Grades (LEFT JOIN):' as Test;
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    C.Code,
    CASE WHEN G.Grade IS NULL THEN 'No Grade Yet' ELSE CAST(G.Grade AS VARCHAR) END AS Grade
FROM Students S
JOIN Enrollments E ON S.StudentID = E.StudentID
JOIN Courses C ON E.CourseID = C.CourseID
LEFT JOIN Grades G ON S.StudentID = G.StudentID AND C.CourseID = G.CourseID
ORDER BY S.LastName;

-- =============================================
-- SAMPLE PRACTICE QUERIES
-- =============================================

PRINT '=== PRACTICE QUERY EXAMPLES ===';

-- Example 1: Find all Computer Science students
SELECT 'Computer Science Students:' as Example;
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    S.Email
FROM Students S
JOIN Departments D ON S.DepartmentID = D.DepartmentID
WHERE D.Name = 'Computer Science';

-- Example 2: Find courses with 4 credits
SELECT 'Courses with 4 Credits:' as Example;
SELECT Code, Name, Credits FROM Courses WHERE Credits = 4;

-- Example 3: Find students with GPA above 3.5
SELECT 'Students with High GPA:' as Example;
SELECT
    S.FirstName + ' ' + S.LastName AS StudentName,
    AVG(G.Grade) AS GPA
FROM Students S
JOIN Grades G ON S.StudentID = G.StudentID
GROUP BY S.StudentID, S.FirstName, S.LastName
HAVING AVG(G.Grade) > 3.5
ORDER BY GPA DESC;

-- Example 4: Course enrollment summary
SELECT 'Course Enrollment Summary:' as Example;
SELECT
    C.Code,
    C.Name AS CourseName,
    COUNT(E.StudentID) AS EnrolledStudents,
    COUNT(G.Grade) AS StudentsWithGrades
FROM Courses C
LEFT JOIN Enrollments E ON C.CourseID = E.CourseID
LEFT JOIN Grades G ON C.CourseID = G.CourseID
GROUP BY C.CourseID, C.Code, C.Name
ORDER BY EnrolledStudents DESC;

PRINT '=== ALL TESTS COMPLETED SUCCESSFULLY! ===';
PRINT 'Your fundamental university database is working correctly.';
PRINT 'You can now practice with the query examples in query_examples.sql';

-- =============================================
-- NEXT STEPS FOR LEARNING
-- =============================================
/*
Now that your database is set up, try these learning exercises:

1. Practice basic SELECT statements
2. Learn different types of JOINs (INNER, LEFT, RIGHT)
3. Practice GROUP BY and aggregate functions
4. Try WHERE clauses with different conditions
5. Practice ORDER BY for sorting results
6. Learn to use views for complex queries

Check the query_examples.sql file for more practice queries!
*/
